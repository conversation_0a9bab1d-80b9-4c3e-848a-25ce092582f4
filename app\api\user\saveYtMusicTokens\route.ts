import { NextRequest, NextResponse } from "next/server";
import { UpdateUserYtMusicTokens, GetUserByYtMusicId, CreateUser } from "../business";
import { JC_Utils_Security, JC_Utils } from "@/app/Utils";
import { YT_GetUserId } from "../../ytGetUserId/business";
import { UserModel } from "@/app/models/User";
import { CreateUserConfig } from "../../userConfig/business";
import { UserConfigModel } from "@/app/models/UserConfig";

export async function POST(request: NextRequest) {
    try {
        const { authToken, cookie } = await request.json();

        if (!authToken || !cookie) {
            return NextResponse.json({ error: "Auth token and cookie are required" }, { status: 400 });
        }

        // Encrypt the tokens before storing
        const encryptedAuthToken = JC_Utils_Security.encryptYtMusicToken(authToken);
        const encryptedCookie = JC_Utils_Security.encryptYtMusicToken(cookie);

        // Get YT Music user ID using the provided tokens
        const ytMusicUserId = await YT_GetUserId(authToken, cookie);

        if (!ytMusicUserId) {
            // Return null so frontend shows error toast
            return NextResponse.json(null, { status: 200 });
        }

        // Try to find user by YT Music ID
        const existingUser = await GetUserByYtMusicId(ytMusicUserId);

        if (existingUser) {
            // Case 3: YT Music ID found and matching user exists - update tokens and auto-login
            await UpdateUserYtMusicTokens(existingUser.Id, encryptedAuthToken, encryptedCookie);

            // Return success with user info for automatic login
            return NextResponse.json({
                status: 200,
                message: "YT Music tokens saved successfully",
                autoLogin: true,
                user: {
                    id: existingUser.Id,
                    email: existingUser.Email,
                    ytMusicId: ytMusicUserId
                }
            });
        } else {
            // Case 2: YT Music ID found but no User record exists - auto-create user and auto-login
            const newUserId = JC_Utils.generateGuid();
            const newUser = new UserModel({
                Id: newUserId,
                YtMusicId: ytMusicUserId,
                FirstName: "",
                LastName: "",
                Email: `ytmusic_${ytMusicUserId}@temp.com`, // Temporary email
                YtMusicAuthTokenHash: encryptedAuthToken,
                YtMusicCookieHash: encryptedCookie,
                IsAdmin: false,
                IsEmailSubscribed: false,
                IsDiscountUser: false,
                StripeCustomerId: "",
                IsVerified: true // Auto-verify since they authenticated via YT Music
            });

            // Create the user in the database
            await CreateUser(newUser);

            // Create a default UserConfig for the new user
            const newUserConfig = new UserConfigModel({
                UserId: newUser.Id,
                NominatedToListenPlaylistUrl: null,
                SelectedPlaylistIdListJson: null,
                HiddenPlaylistIdListJson: null,
                NominatedNewSongsPlaylistUrl: null,
                NewSongsProcessDateLastRun: null
            });
            await CreateUserConfig(newUserConfig);

            // Return success with user info for automatic login
            return NextResponse.json({
                status: 200,
                message: "YT Music account created and tokens saved successfully",
                autoLogin: true,
                user: {
                    id: newUser.Id,
                    email: newUser.Email,
                    ytMusicId: ytMusicUserId
                }
            });
        }

    } catch (error) {
        console.error("Error saving YT Music tokens:", error);
        return NextResponse.json({ error: "Failed to save YT Music tokens" }, { status: 500 });
    }
}
