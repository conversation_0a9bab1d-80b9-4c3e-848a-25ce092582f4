import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { CreateUserConfig, GetUserConfig, UpdateUserConfig } from "./business";
import { UserConfigModel } from "@/app/models/UserConfig";

// Get
export async function GET(request: NextRequest) {
    try {

        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId")!;
        const result = await GetUserConfig(userId);
        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Create
export async function PUT(request: NextRequest) {
    try {

        const userConfigData:UserConfigModel = await request.json();
        await CreateUserConfig(userConfigData);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Update
export async function POST(request: NextRequest) {
    try {

        const userConfigData:UserConfigModel = new UserConfigModel(await request.json());
        // First check if exists
        if ((await GetUserConfig(userConfigData.UserId)) == null) {
            await CreateUserConfig(userConfigData);
        } else {
            await UpdateUserConfig(userConfigData);
        }
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}