@import '../../global';

.checkoutButtonContainer {
    position: relative;
    width: 80px;
    height: 105px;
    display: flex;
    flex-direction: column;
    row-gap: 2px;
    align-items: center;
    cursor: pointer;
    .bagIcon {
        position: absolute;
        top: 0;
        left: 50%; transform: translateX(-50%);
        width: auto;
        height: 78%;
        z-index: 10;
        transition: height 0.2s,
                    opacity 0.2s;
    }
    .checkoutText {
        position: absolute;
        height: max-content;
        bottom: 4px;
        font-size: 16px;
        font-weight: bold;
    }
}

// Login/Register
.loginRegisterContainer {
    width: max-content;
    height: max-content;
    cursor: pointer;

    .loginRegisterText {
        padding: 12px 0;
        font-size: 25px;
        font-weight: bold;
    }
    &:hover {
        color: $primaryColor;
    }
}


// - SCREEN SIZES - //

@media (max-width: $tinyScreenSize) {
    .checkoutButtonContainer {
        width: 60px !important;
        height: 65px !important;
        row-gap: 0px !important;
        .bagIcon {
            height: 75%;
        }
        .checkoutText {
            font-size: 11px;
            bottom: 3px;
        }
    }
}