import { NextRequest, NextResponse } from "next/server";
import { YT_RemoveAllFromPlaylist } from "./business";
import { auth } from "@/app/auth";
import { GetUserYtMusicTokens } from "../user/business";

// Update
export async function POST(request: NextRequest) {
    try {
        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get request data
        const requestData:{ playlistUrl:string } = await request.json();

        // Validate required parameters
        if (!requestData.playlistUrl) {
            return NextResponse.json({ error: "Missing playlistUrl parameter" }, { status: 400 });
        }

        // Get user's YT Music tokens
        const { authToken, cookie } = await GetUserYtMusicTokens(session.user.id);

        // Call business logic with auth credentials
        await YT_RemoveAllFromPlaylist(requestData.playlistUrl, authToken, cookie);

        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}