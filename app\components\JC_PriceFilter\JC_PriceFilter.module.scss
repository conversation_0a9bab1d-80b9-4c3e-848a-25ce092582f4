@import '../../global';

$leftGap: 10px;

.mainContainer {
    position: relative;
    width: max-content;
    height: max-content;
    user-select: none;

    .mainButton {
        @include dropdownOptionContainer;
        position: relative;
        padding-left: 14px;
        padding-right: 14px;
        box-sizing: border-box;
        column-gap: 2px;
        cursor: pointer;

        .optionIcon {
            @include dropdownIcon;
        }
    }

    .dropdown {
        @include dropdownOptionContainer;
        @include dropdownDropdown;
        height: max-content;
        width: max-content;
        padding: 10px 22px;
        display: grid;
        grid-template-columns: max-content max-content;
        justify-content: center;
        justify-items: end;
        column-gap: 12px;
        row-gap: 8px;
    }

}