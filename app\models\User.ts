import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class UserModel extends _Base implements _ModelRequirements {

    static tableName: string = "User";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "FirstName";
    // static cacheMinutes_get: number = 30;
    // static cacheMinutes_getList: number = 5;

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<UserModel>(UserModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<UserModel>(UserModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: UserModel) {
        return await JC_Put<UserModel>(UserModel, this.apiRoute, data);
    }
    static async CreateList(dataList: UserModel[]) {
        return await JC_PutRaw<UserModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "User");
    }
    static async Update(data: UserModel) {
        return await JC_Post<UserModel>(UserModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: UserModel[]) {
        return await JC_PostRaw<UserModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "User");
    }
    static async Delete(id: string) {
        return await JC_Delete(UserModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "User");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    YtMusicId: string;
    FirstName: string;
    LastName: string;
    Email: string;
    YtMusicAuthTokenHash: string;
    YtMusicCookieHash: string;
    IsAdmin: boolean;
    IsEmailSubscribed: boolean;
    IsDiscountUser: boolean;
    StripeCustomerId: string;
    IsVerified: boolean;
    VerificationToken?: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.YtMusicId = "";
        this.FirstName = "";
        this.LastName = "";
        this.Email = "";
        this.YtMusicAuthTokenHash = "";
        this.YtMusicCookieHash = "";
        this.IsAdmin = false;
        this.IsEmailSubscribed = false;
        this.IsDiscountUser = false;
        this.StripeCustomerId = "";
        this.IsVerified = false;
        this.VerificationToken = undefined;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new UserModel());
    }

    static jcFieldTypeforField(fieldName: keyof UserModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "YtMusicId":
                return FieldTypeEnum.Text;
            case "FirstName":
                return FieldTypeEnum.Text;
            case "LastName":
                return FieldTypeEnum.Text;
            case "Email":
                return FieldTypeEnum.Email;
            case "YtMusicAuthTokenHash":
                return FieldTypeEnum.Text;
            case "YtMusicCookieHash":
                return FieldTypeEnum.Text;
            case "IsAdmin":
                return FieldTypeEnum.Text;
            case "IsEmailSubscribed":
                return FieldTypeEnum.Text;
            case "IsDiscountUser":
                return FieldTypeEnum.Text;
            case "StripeCustomerId":
                return FieldTypeEnum.Text;
            case "IsVerified":
                return FieldTypeEnum.Text;
            case "VerificationToken":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.FirstName} ${this.LastName} | ${this.Email}`;
    }

}

export function D_User():UserModel {
    return new UserModel();
}
