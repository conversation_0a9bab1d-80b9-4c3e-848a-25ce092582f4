// "use client"

// // Styles
// import styles from "./JC_SearchBar.module.scss";
// // React
// import React from "react";
// import { ChangeEvent, useEffect, useState } from "react";
// // Next
// import Image from "next/image";
// import { useRouter } from 'next/navigation'
// // Components
// import JC_Button from "../JC_Button/JC_Button";
// // Services
// import { JC_GetList } from "@/app/apiServices/JC_GetList";
// // Utils
// import { IsOnPage, StringNullOrEmpty, searchMatches } from "../../Utils";
// import { ProductModel } from "@/app/models/Product";
// import { ProductGroupModel } from "@/app/models/ProductGroup";
// // Enums
// import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";

// export default function JC_SearchBar() {

//     // - STATE - //

//     const router = useRouter();
//     const [searchText, setSearchText] = useState<string>("");
//     const [resultsOpen, setResultsOpen] = useState(false);
//     const [products, setProducts] = useState<ProductModel[]>([]);
//     const [filteredProducts, setFilteredProducts] = useState<ProductModel[] | null>(null);


//     // - INITIALISE - //

//     useEffect(() => {
//         JC_GetList<ProductGroupModel>("productGroup", {}, LocalStorageKeyEnum.JC_ProductGroups).then(list => {
//             // Get all Products out of each ProductGroup
//             let productList:ProductModel[] = ([] as ProductModel[]).concat(...list.map(x => x.Ex_ProductList)).sort((a,b) => a.Name > b.Name ? 1 : -1)
//             productList.forEach(p => p.UI_GroupHasMultipleProducts = list.find(g => g.Id == p.ProductGroupId)!.Ex_ProductList.length > 1)
//             setProducts(productList);
//             setFilteredProducts(productList);
//         });
//     }, []);


//     // - HANDLES - //

//     // Get Results
//     function getResults(search:string) {
//         setSearchText(search);
//         // Must have at least 3 characters to show results
//         if (search == null || search.length == 0) {
//             setFilteredProducts(products);
//         // ELSE search on any Group Name, Product Name or Variant Name that contains any of the words in the search
//         } else {
//             let newList:ProductModel[] = products.filter(product =>
//                    searchMatches(search, product.Ex_GroupName)
//                 || searchMatches(search, product.Name)
//                 || product.Ex_Variations.some(variant => searchMatches(search, variant.Name))
//             );
//             setFilteredProducts(newList);
//         }
//     }

//     // Reset
//     function resetBar() {
//         setSearchText("");
//         setResultsOpen(false);
//         setFilteredProducts(products);
//         document.getElementById("searchBarInput")!.blur();
//     }


//     // - MAIN - //

//     return (

//         <div className={styles.mainContainer}>

//             {/* Outside Click Div */}
//             {resultsOpen && <div className={styles.outsideClickDiv} onClick={() => resetBar()} />}
            
//             {/* Bar */}
//             <div className={styles.bar} style={resultsOpen ? { zIndex: 99 } : {}}>

//                 {/* Input */}
//                 <input
//                     id="searchBarInput"
//                     placeholder="Search..."
//                     value={searchText}
//                     onFocus={() => setResultsOpen(true)}
//                     onChange={(event:ChangeEvent<HTMLInputElement>) => getResults(event.target?.value)}
//                 />

//                 {/* Icon */}
//                 <Image
//                     src={`/icons/Search.png`}
//                     width={100}
//                     height={100}
//                     className={styles.icon}
//                     alt="Search icon"
//                 />

//             </div>

//             {/* Results */}
//             {resultsOpen && 
//             <div className={styles.results}>

//                 <div className={styles.resultsScrollDiv}>

//                     {/* Results Tiles */}
//                     {filteredProducts != null && filteredProducts.length > 0 &&
//                     filteredProducts.map((product:ProductModel) =>
//                         <div
//                             key={product.Id}
//                             className={styles.resultTile}
//                             onClick={() => {
//                                 router.push(`product?id=${product.Id}`);
//                                 resetBar();
//                             }}
//                         >
//                             {/* Price */}
//                             <div className={styles.resultPrice}>
//                                 ${23.50.toFixed(2)}
//                             </div>
//                             {/* Image */}
//                             <Image
//                                 className={styles.resultImage}
//                                 src={!StringNullOrEmpty(product.ImageFileName) ? `/products/products/${product.ImageFileName}.jpg` : `/products/groups/${product.Ex_GroupImageFileName}.jpg`}
//                                 width={50}
//                                 height={50}
//                                 alt={product.Name}
//                             />
//                             {/* Name */}
//                             <div className={styles.resultName}>
//                                 {product.Name}
//                             </div>
//                             {/* Group Button */}
//                             {product.UI_GroupHasMultipleProducts &&
//                             <JC_Button
//                                 overrideClass={styles.resultGroupButton}
//                                 text={product.Ex_GroupName}
//                                 linkToPage={`products?id=${product.ProductGroupId}`}
//                                 onClick={(event) => {
//                                     if (!IsOnPage(`products?id=${product.ProductGroupId}`)) {
//                                         resetBar();
//                                     }
//                                     event.stopPropagation();
//                                 }}
//                                 isSecondary
//                                 isSmall
//                             />}
//                         </div>
//                     )}

//                     {/* No Results (searched but no results) */}
//                     {filteredProducts != null && filteredProducts.length == 0 &&
//                     <div className={styles.noResultsMessage}>
//                         <div className={styles.noResultsText}>No Results</div>
//                     </div>}

//                 </div>

//             </div>}

//         </div>

//     );
// }
