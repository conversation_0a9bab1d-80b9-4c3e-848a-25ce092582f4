export class JC_DropdownOptionModel {
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    OptionId: string;
    Label: string;
    IconName?: string;
    Selected?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<JC_DropdownOptionModel>) {
        this.OptionId = "";
        this.Label = "";
        this.IconName = undefined;
        this.Selected = false;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Label;
    }
}