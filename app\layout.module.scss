@import './global';

// Root
.rootMainContainer {
    padding: 40px 0 0 40px;
    box-sizing: border-box;
    width: max-content;
    max-width: 97vw;
    height: 97vh;
    font-family: var(--font-inter); // Font family used everywhere, can only set this if have "inter.variable" in ts
    background-color: $offBlack;
    overflow-y: scroll;
    overflow-x: hidden;

    // Big Title
    .mainTitle {
        margin-bottom: 40px;
        width: max-content;
        border: solid $largeBorderWidth $primaryColor;
        border-radius: $smallBorderRadius;
        padding: 10px 50px;
        background-color: $veryDarkPrimaryColor;
        font-size: 80px;
        color: $primaryColor;
        font-weight: bold;
        user-select: none;
    }

    // Every Page
    .pageContainer {
        display: flex;
        position: relative;
        box-sizing: border-box;
    }

    // textarea
    textarea {
        font-family: var(--font-inter);
    }

    // a
    a {
        color: black;
        text-decoration: none;
    }
}