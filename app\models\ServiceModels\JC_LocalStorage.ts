import { LocalStorageKeyEnum } from "@/app/enums/LocalStorageKey";

export class JC_LocalStorageModel {
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    StorageKey: LocalStorageKeyEnum;
    ResetInterval?: number;
    ForceReset?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<JC_LocalStorageModel>) {
        this.StorageKey = LocalStorageKeyEnum.JC_YtMusicPlaylists;
        this.ResetInterval = undefined;
        this.ForceReset = false;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.StorageKey.toString();
    }
}