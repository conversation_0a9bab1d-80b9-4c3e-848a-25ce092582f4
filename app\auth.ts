import NextAuth, { CredentialsSignin, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, NextAuthConfig } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import GoogleProvider from 'next-auth/providers/google'
import {} from "next-auth/jwt";
import { UserModel } from "./models/User";
import { GetUserByEmail, GetUser, GetUserByYtMusicId } from "./api/user/business";
import { GetGlobalSetting, UpdateGlobalSettingsValue } from "./api/globalSettings/business";
import { JC_Utils_Dates } from "./Utils";

declare module 'next-auth' {
    interface User extends UserModel {}
    interface Session {
        user: UserModel & DefaultSession["user"];
    }
}

declare module "next-auth/jwt" {
    /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
    interface JWT {
        dbUser: UserModel;
    }
}

export const authOptions:NextAuthConfig = {
    providers: [
        Credentials({
            // You can specify which fields should be submitted, by adding keys to the `credentials` object.
            // e.g. domain, username, password, 2FA token, etc.
            credentials: {
                email: {},
                password: {},
                ytMusicId: {}
            },

            authorize: async (credentials) => {
                if (!credentials) {
                    throw new Error("No credentials supplied.");
                }

                // If YT Music ID is provided, authenticate via YT Music (no password required)
                if (credentials.ytMusicId) {
                    let user:UserModel = await GetUserByYtMusicId(credentials.ytMusicId as string);
                    if (!user) {
                        throw new InvalidLoginError("User not found.");
                    }
                    return {
                        ...user,
                        id: user.Id
                    };
                }

                // Traditional email authentication (no password required anymore)
                let user:UserModel = await GetUserByEmail(credentials.email as string);
                // Check if user exists
                if (!user) {
                    throw new InvalidLoginError("User not found.");
                }
                else {
                    // Return
                    return {
                        ...user,
                        id: user.Id
                    };
                }
            },
        }),

        // GoogleProvider({
        //   clientId: process.env.GOOGLE_CLIENT_ID as string,
        //   clientSecret: process.env.GOOGLE_CLIENT_SECRET as string,
        // })

    ],
    callbacks: {

        // async signIn({ user, account, profile }) {
        //     var test = "0 mate";
        //     return true;``
        //     // const response = await axios.post(
        //     //     process.env.NEXT_PUBLIC_API_BASE_URL + "/auth/userExists",
        //     //     { email: profile?.email }
        //     // );
        //     // if (response && response.data?.value === true) {
        //     //     return true;
        //     // } else {
        //     //     const data = {
        //     //     firstName: profile.given_name,
        //     //     lastName: profile.family_name,
        //     //     email: profile.email,
        //     //     profileUrl: profile.picture,
        //     //     };
        //     //     const response = await axios.post(
        //     //     process.env.NEXT_PUBLIC_API_BASE_URL + "/auth/signup",
        //     //     data
        //     //     );
        //     //     return true;
        //     // }
        // },
        async jwt({ token, user, trigger, session }) {
            // IF user exists (means user just logged in) OR force refresh user is set, get extra fields from DB and save to JWT
            if (user || (await GetGlobalSetting("ForceRefreshAuthToken")).Value == "1") {
                let dbUser:UserModel = await GetUser(token.sub as string);
                token.dbUser = dbUser;
                await UpdateGlobalSettingsValue("ForceRefreshAuthToken", "0");
            }
            return token;
        },
        async session({ session, user, token }) {
            // Get all User data from token, including extra DB data added in "jwt()" callback
            session.user = {
                ...token.dbUser,
                id: token.sub as string,
                email: token.dbUser.Email,
                emailVerified: null
            };
            return session;
        }

    }
};

export const { handlers, signIn, signOut, auth } = NextAuth(authOptions);


// - ERRORS = //

export class InvalidLoginError extends CredentialsSignin {
    code = 'invalid_credentials'
}
export class UserLockedOutError extends CredentialsSignin {
    code = 'user_locked_out'
}