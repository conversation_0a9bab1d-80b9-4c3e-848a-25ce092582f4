{
    responseContext: {
      visitorData: "Cgt4OFRlWkNpRXpMayizpJnABjIKCgJBVRIEGgAgPQ%3D%3D",
      serviceTrackingParams: [
        {
          service: "GFEEDBACK",
          params: [
            {
              key: "browse_id",
              value: "FEmusic_history",
            },
            {
              key: "browse_id_prefix",
              value: "",
            },
            {
              key: "logged_in",
              value: "0",
            },
            {
              key: "e",
              value: "23804281,24004644,24077241,24181174,24241378,24290153,24407444,24439361,24459436,24499533,24566687,51010235,51020570,51025415,51037342,51037349,51063643,51065188,51079992,51089007,51098299,51115184,51152050,51176511,51178320,51178333,51178340,51178353,51183909,51204329,51222973,51226842,51227037,51228850,51237842,51242448,51249749,51256074,51311027,51311034,51313109,51313767,51324733,51337456,51338029,51340662,51341228,51342857,51349914,51351446,51353393,51354083,51354114,51354569,51359179,51360097,51360104,51360119,51360134,51361830,51362071,51362857,51364291,51366423,51367487,51369906,51372971,51375205,51375647,51375719,51380374,51380383,51380400,51381346,51385023,51386361,51386500,51386540,51389033,51389629,51391339,51394774,51394781,51397091,51397104,51397257,51397332,51398232,51400154,51401549,51402072,51404808,51404810,51405542,51406710,51407629,51407634,51413046,51413053,51413062,51413067,51413072,51413081,51413088,51414152,51417452,51417471,51417480,51417493,51417504,51417521,51417659,51419901,51420240,51420702,51420967,51421832,51423139,51423432,51425617,51426028,51426097,51428417,51428624,51431534,51432394,51432625,51433499,51433823,51435845,51435879,51435884,51435893,51435903,51435910,51435922,51438208,51438828,51439763,51439874,51440160,51440688,51440725,51440852,51441597,51441712,51441840,51442265,51442501,51445006,51445306,51445564,51445575,51445580,51445591,51445602,51445613,51445618,51446241,51447191,51447614,51448334,51448643,51449148,51449911,51450811,51451043,51451295,51452481,51452495,51453930,51454238,51455008,51455918,51455921,51456421,51456628,51457083,51457461,51458212,51458922,51458927,51459385,51460216,51461317,51461446,51463226,51463929,51464700,51464787,51464994,51465936,51467274,51468320",
            },
          ],
        },
        {
          service: "CSI",
          params: [
            {
              key: "c",
              value: "WEB_REMIX",
            },
            {
              key: "cver",
              value: "1.20241016.01.00",
            },
            {
              key: "yt_li",
              value: "0",
            },
            {
              key: "GetBrowseHistoryPage_rid",
              value: "0xba45fbf47cf3b23e",
            },
          ],
        },
        {
          service: "ECATCHER",
          params: [
            {
              key: "client.version",
              value: "1.20000101",
            },
            {
              key: "client.name",
              value: "WEB_REMIX",
            },
          ],
        },
      ],
    },
    contents: {
      singleColumnBrowseResultsRenderer: {
        tabs: [
          {
            tabRenderer: {
              selected: true,
              content: {
                sectionListRenderer: {
                  contents: [
                    {
                      itemSectionRenderer: {
                        contents: [
                          {
                            messageRenderer: {
                              text: {
                                runs: [
                                  {
                                    text: "Sign in to view your history",
                                  },
                                ],
                              },
                              icon: {
                                iconType: "WATCH_HISTORY",
                              },
                              trackingParams: "CAQQljsYACITCLnrt56n6YwDFbMutwAdqhgfDw==",
                              button: {
                                buttonRenderer: {
                                  style: "STYLE_DEFAULT",
                                  isDisabled: false,
                                  text: {
                                    runs: [
                                      {
                                        text: "Sign in",
                                      },
                                    ],
                                  },
                                  navigationEndpoint: {
                                    clickTrackingParams: "CAUQ8FsiEwi567eep-mMAxWzLrcAHaoYHw8=",
                                    signInEndpoint: {
                                      hack: true,
                                    },
                                  },
                                  trackingParams: "CAUQ8FsiEwi567eep-mMAxWzLrcAHaoYHw8=",
                                },
                              },
                              subtext: {
                                messageSubtextRenderer: {
                                  text: {
                                    runs: [
                                      {
                                        text: "Revisit old favorites and discover new songs",
                                      },
                                    ],
                                  },
                                },
                              },
                              style: {
                                value: "RENDER_STYLE_VERTICAL_CENTERED",
                              },
                            },
                          },
                        ],
                        trackingParams: "CAMQuy8YACITCLnrt56n6YwDFbMutwAdqhgfDw==",
                      },
                    },
                  ],
                  trackingParams: "CAIQui8iEwi567eep-mMAxWzLrcAHaoYHw8=",
                },
              },
              trackingParams: "CAEQ8JMBGAAiEwi567eep-mMAxWzLrcAHaoYHw8=",
            },
          },
        ],
      },
    },
    trackingParams: "CAAQhGciEwi567eep-mMAxWzLrcAHaoYHw8=",
  }