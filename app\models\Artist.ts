import { ArtistItemModel } from "./ArtistItem.js";
import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class ArtistModel extends _Base implements _ModelRequirements {

    static tableName: string = "Artist";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Name";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<ArtistModel>(ArtistModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<ArtistModel>(ArtistModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: ArtistModel) {
        return await JC_Put<ArtistModel>(ArtistModel, this.apiRoute, data);
    }
    static async CreateList(dataList: ArtistModel[]) {
        return await JC_PutRaw<ArtistModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "Artist");
    }
    static async Update(data: ArtistModel) {
        return await JC_Post<ArtistModel>(ArtistModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: ArtistModel[]) {
        return await JC_PostRaw<ArtistModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "Artist");
    }
    static async Delete(id: string) {
        return await JC_Delete(ArtistModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "Artist");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Name: string;
    ImageUrl: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ArtistModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Name = "";
        this.ImageUrl = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new ArtistModel());
    }

    static jcFieldTypeforField(fieldName: keyof ArtistModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "Name":
                return FieldTypeEnum.Text;
            case "ImageUrl":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}

export class ArtistFullModel extends ArtistModel {
    static tableName: string = "ArtistFull";
    static apiRoute: string = "ytGetArtist";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Name";

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Albums: ArtistItemModel[];
    Singles: ArtistItemModel[];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ArtistFullModel>) {
        super(init);
        this.Albums = [];
        this.Singles = [];
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new ArtistFullModel());
    }

    static jcFieldTypeforField(fieldName: keyof ArtistFullModel) {
        switch (fieldName) {
            case "Albums":
                return FieldTypeEnum.Text;
            case "Singles":
                return FieldTypeEnum.Text;
            default:
                return super.jcFieldTypeforField(fieldName as keyof ArtistModel);
        }
    }
}