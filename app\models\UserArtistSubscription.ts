import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";
import { ArtistModel } from "./Artist";
import { ArtistItemModel } from "./ArtistItem";

export class UserArtistSubscriptionModel extends _Base implements _ModelRequirements {

    static tableName: string = "UserArtistSubscription";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "ArtistName";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<UserArtistSubscriptionModel>(UserArtistSubscriptionModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<UserArtistSubscriptionModel>(UserArtistSubscriptionModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: UserArtistSubscriptionModel) {
        return await JC_Put<UserArtistSubscriptionModel>(UserArtistSubscriptionModel, this.apiRoute, data);
    }
    static async CreateList(dataList: UserArtistSubscriptionModel[]) {
        return await JC_PutRaw<UserArtistSubscriptionModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "UserArtistSubscription");
    }
    static async Update(data: UserArtistSubscriptionModel) {
        return await JC_Post<UserArtistSubscriptionModel>(UserArtistSubscriptionModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: UserArtistSubscriptionModel[]) {
        return await JC_PostRaw<UserArtistSubscriptionModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "UserArtistSubscription");
    }
    static async Delete(id: string) {
        return await JC_Delete(UserArtistSubscriptionModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "UserArtistSubscription");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId: string;
    ArtistId: string;
    ArtistName: string;
    CheckedSingleIdListJson: string;
    CheckedAlbumIdListJson: string;

    // Extended
    Artist?: ArtistModel;
    CheckedSingleIdList?: string[];
    CheckedAlbumIdList?: string[];

    // UI
    NewSingles?:ArtistItemModel[];
    NewAlbums?:ArtistItemModel[];
    Checked?:boolean;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserArtistSubscriptionModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.UserId = "";
        this.ArtistId = "";
        this.ArtistName = "";
        this.CheckedAlbumIdListJson = "[]";
        this.CheckedSingleIdListJson = "[]";
        this.CheckedSingleIdList = [];
        this.CheckedAlbumIdList = [];
        this.NewSingles = [];
        this.NewAlbums = [];
        this.Checked = false;
        Object.assign(this, init);
        // Extended
        this.Artist = init?.Artist ? new ArtistModel(init.Artist) : undefined;
    }

    static getKeys() {
        return Object.keys(new UserArtistSubscriptionModel());
    }

    static jcFieldTypeforField(fieldName: keyof UserArtistSubscriptionModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "UserId":
                return FieldTypeEnum.Text;
            case "ArtistId":
                return FieldTypeEnum.Text;
            case "ArtistName":
                return FieldTypeEnum.Text;
            case "CheckedSingleIdListJson":
                return FieldTypeEnum.Text;
            case "CheckedAlbumIdListJson":
                return FieldTypeEnum.Text;
            case "Checked":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.ArtistName;
    }
}

export function D_UserArtistSubscription(userId:string):UserArtistSubscriptionModel {
    return new UserArtistSubscriptionModel({ UserId: userId });
}
