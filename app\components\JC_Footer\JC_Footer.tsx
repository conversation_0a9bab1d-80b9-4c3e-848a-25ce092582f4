import styles from "./JC_Footer.module.scss";
import React from "react";
import Link from "next/link";
import Image from "next/image";
import JC_Button from "../JC_Button/JC_Button";

export default function JC_Footer() {
    return (

        <div className={styles.mainContainer} id="JC_footer">

            <div className={styles.innerContainer}>

                {/* Nav Buttons */}
                <JC_Button linkToPage="" text="Home" />

                {/* Social */}
                <div className={styles.socialContainer}>
                </div>

            </div>

        </div>

    );
}
