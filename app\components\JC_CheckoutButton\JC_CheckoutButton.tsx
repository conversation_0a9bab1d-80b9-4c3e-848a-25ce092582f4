"use client"

import styles from "./JC_CheckoutButton.module.scss";
import React from 'react';
import Link from 'next/link';
import Image from "next/image";

export default function JC_CheckoutButton() {
    return (

        <Link href="/checkout">

            <div className={styles.checkoutButtonContainer}>

                {/* Icon */}
                <Image
                    src="/icons/Bag.png"
                    width={100}
                    height={100}
                    className={styles.bagIcon}
                    alt="Bag"
                />

                {/* Text */}
                <div className={styles.checkoutText}>Checkout</div>

            </div>

        </Link>

    );
}
