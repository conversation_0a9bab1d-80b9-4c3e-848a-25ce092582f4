import { SongModel } from '@/app/models/Song';
import { JC_Utils, JC_Utils_YtMusic } from '@/app/Utils';

export const YT_GetItemSongs = async function (itemId:string, isPlaylist:boolean = false, authorization: string, cookie: string) : Promise<SongModel[]> {

    // Setup call
    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        "browseId": itemId
    };

    // Call with ytMusicHeaders to ensure proper headers
    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    // Get Songs out of json response
    if (isPlaylist) {
        let songsData = JC_Utils.findKeyValue(res.contents, "musicPlaylistShelfRenderer").contents;
        if (songsData != null && songsData.length > 0) {
            let songs:SongModel[] = songsData.map((data:any) => ({
                Id: JC_Utils.findKeyValue(data, "videoId"),
                PlaylistSetVideoId: JC_Utils.findKeyValue(data, "playlistSetVideoId"),
                Title: JC_Utils.findKeyValue(data, "flexColumns")[0].musicResponsiveListItemFlexColumnRenderer.text.runs[0].text,
                Artist: JC_Utils.findKeyValue(data, "flexColumns")[1].musicResponsiveListItemFlexColumnRenderer.text.runs[0].text,
                ImageUrl: JC_Utils.findKeyValue(data, "thumbnails").pop().url
            }) as SongModel);
            return songs;
        } else {
            return [];
        }
    } else {
        let songsData = JC_Utils.findKeyValue(res.contents, "musicShelfRenderer").contents
        if (songsData != null && songsData.length > 0) {
            let artistName = JC_Utils.findKeyValue(res.contents, "straplineTextOne").runs[0].text;
            let imageUrl = JC_Utils.findKeyValue(res.contents, "thumbnails").pop().url;
            let songs:SongModel[] = songsData.map((data:any) => ({
                Id: JC_Utils.findKeyValue(data, "videoId"),
                PlaylistSetVideoId: JC_Utils.findKeyValue(data, "playlistSetVideoId"),
                Title: JC_Utils.findKeyValue(data, "flexColumns")[0].musicResponsiveListItemFlexColumnRenderer.text.runs[0].text,
                Artist: artistName,
                ImageUrl: imageUrl
            }) as SongModel);
            return songs;
        } else {
            return [];
        }
    }

}