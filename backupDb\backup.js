async function Backup() {

    // Imports
    const fs = require('fs');
    // var put = require('@vercel/blob').put;
    var exec = require('child_process').exec;

    // Backup
    let dbBackupString = "";
    await new Promise((resolve) => {
        exec(
            `pg_dump --schema=public --inserts --clean --no-privileges --if-exists "postgres://${process.env.POSTGRES_USER}:${process.env.POSTGRES_PASSWORD}@${process.env.POSTGRES_HOST}/verceldb`,
            (error, stdout, stderr) => {
                dbBackupString = stdout != null && stdout != "" ? stdout : stderr;
                resolve("Received DB backup successfully.");
            }
        );
    });

    // Do not drop schema
    dbBackupString = dbBackupString.replace("DROP SCHEMA IF EXISTS public;", "");
    dbBackupString = dbBackupString.replace("CREATE SCHEMA public;", "");
    dbBackupString = dbBackupString.replace("ALTER SCHEMA public OWNER TO pg_database_owner;", "");
    dbBackupString = dbBackupString.replace("COMMENT ON SCHEMA public IS 'standard public schema';", "");

    // Save to file
    let backupNum = 1;
    while (fs.existsSync(`backups/backup${backupNum}.sql`)) {
        backupNum++;
    }
    fs.writeFile(`backups/backup${backupNum}.sql`, dbBackupString, err => {
        if (err) {
            return console.log(err);
        }
        console.log("The file was saved!");
    }); 

    // Save to blob storage
    // await put(
    //     `dbBackups/ckDbBackup (${dateTimeString}).sql`,
    //     dbBackupString,
    //     { access: 'public', token: "vercel_blob_rw_AnRA1v2hryhEKNXu_TAInFvGcLnw3FIKZ4RtHzcd2ymsexz" }
    // );
}

Backup();