import { ArtistModel } from "@/app/models/Artist";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";
import { JC_Utils, JC_Utils_YtMusic } from "@/app/Utils";

export const YT_GetSubscriptions = async function (authorization: string, cookie: string) : Promise<ArtistModel[]> {

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        "browseId": "FEmusic_library_corpus_artists"
    };

    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    let allitemsData = [];

    let itemsData = JC_Utils.findKeyValue(res.contents, "musicShelfRenderer");
    allitemsData.push(...itemsData.contents);

    for (var c of itemsData.continuations ?? []) {
        allitemsData.push(...(await GetSubscriptionsContinuedItems(c, authorization, cookie)));
    };

    let allItems:ArtistModel[] = allitemsData.map(s => new ArtistModel({
        Id: JC_Utils.findKeyValue(s, "browseId"),
        Name: JC_Utils.findKeyValue(JC_Utils.findKeyValue(s, "text"), "text"),
        ImageUrl: JC_Utils.findKeyValue(s, "thumbnails").pop().url
    }));

    // Return
    return allItems;
}



// Get continued items
const GetSubscriptionsContinuedItems = async function (continuation:any, authorization: string = '', cookie: string = ''):Promise<any> {

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext()
    };

    const cRes = (await (await fetch(`https://music.youtube.com/youtubei/v1/browse?ctoken=${continuation.nextContinuationData.continuation}&continuation=${continuation.nextContinuationData.continuation}&itct=${continuation.nextContinuationData.clickTrackingParams}&type=next&&prettyPrint=false`, {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    let cItems = [];

    let cItemsData = cRes.continuationContents.musicShelfContinuation;
    cItems.push(...cItemsData.contents);

    for (var c of cItemsData.continuations ?? []) {
        cItems.push(...(await GetSubscriptionsContinuedItems(c, authorization, cookie)));
    };

    return cItems;

}