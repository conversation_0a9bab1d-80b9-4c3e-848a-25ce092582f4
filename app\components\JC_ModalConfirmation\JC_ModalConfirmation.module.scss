@import '../../global';

$topBottomPadding: 26px;

.modalContentHidden {
    visibility: hidden;
}

// Modal
.modalOverride {
    max-width: 590px !important;

    .loadingSpinner {
        position: absolute;
        left: 0;
        top: 60%; transform: translateY(-50%);
    }
    
    // Main Text
    .mainText {
        width: 100%;
        text-align: center;
        line-height: 24px;
        font-size: 18px;
    }

    // Buttons Containr
    .buttonsContainer {
        margin-top: 18px;
        display: flex;
        justify-content: space-around;
        column-gap: 30px;
        .cancelButton {
            display: inherit;
        }
        .submitButton {
            display: inherit;
        }
    }
}