import styles from "./JC_Header.module.scss";
import React from "react";
import Image from "next/image";
import J<PERSON>_Button from "../JC_Button/JC_Button";
import dynamic from 'next/dynamic';
import { auth } from "@/app/auth";

// Dynamically import the client component
const TokenButton = dynamic(() => import('./TokenButton/TokenButton'), { ssr: false });

export default async function JC_Header() {
    // Get session data on server side
    const session = await auth();

    return (
        <React.Fragment>

            {/* Header */}
            <div className={styles.mainContainer} id="JC_header">

                {/* Logo */}
                <Image
                    src="/logos/Main.png"
                    width={408}
                    height={517}
                    className={styles.logo}
                    alt="Logo"
                />

                {/* Navs */}
                <JC_Button linkToPage="newSongs"       text="New Songs"      />
                <JC_Button linkToPage="addAllArtist"   text="Add All Artist" />
                <JC_Button linkToPage="addLast"        text="Add Last"       />

                {/* Reset Token Button */}
                <TokenButton />
            </div>
        </React.Fragment>
    );
}
