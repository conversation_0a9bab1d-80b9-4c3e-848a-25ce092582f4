{"name": "yt-music-automator", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "npx update-browserslist-db@latest && next build", "start": "next start -p 3000", "lint": "next lint", "postinstall": "npx update-browserslist-db@latest"}, "dependencies": {"@aws-sdk/client-s3": "^3.839.0", "@aws-sdk/s3-request-presigner": "^3.839.0", "@react-google-maps/api": "^2.19.3", "@stripe/react-stripe-js": "^2.7.3", "@stripe/stripe-js": "^4.1.0", "@tiptap/extension-color": "^2.4.0", "@tiptap/extension-text-style": "^2.4.0", "@tiptap/pm": "^2.4.0", "@tiptap/react": "^2.4.0", "@tiptap/starter-kit": "^2.4.0", "@types/bcryptjs": "^2.4.6", "@types/react-date-range": "^1.4.9", "@types/validator": "^13.12.0", "@vercel/blob": "^0.23.3", "@vercel/postgres": "^0.8.0", "@vercel/speed-insights": "^1.0.10", "bcryptjs": "^2.4.3", "date-fns": "^3.6.0", "next": "^14.2.3", "next-auth": "^5.0.0-beta.20", "react": "^18.3.1", "react-date-range": "^2.0.1", "react-dom": "^18", "react-toastify": "^10.0.5", "resend": "^3.4.0", "sharp": "^0.34.2", "stripe": "^16.5.0", "suneditor": "^2.46.3", "suneditor-react": "^3.6.1", "uuid": "^9.0.1", "validator": "^13.12.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/sharp": "^0.32.0", "@types/uuid": "^9.0.8", "eslint": "^8", "eslint-config-next": "14.2.2", "sass": "^1.75.0", "typescript": "^5", "typescript-plugin-css-modules": "^5.1.0"}}