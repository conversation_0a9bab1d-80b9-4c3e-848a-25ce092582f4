import { NextRequest, NextResponse } from "next/server";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";
import { GetGlobalSetting, UpdateGlobalSettingsValue } from "./business";

// Get by "Code"
export async function GET(request: NextRequest) {

    try {
        const params = new URL(request.url).searchParams;
        const code = params.get("code");

        if (!code) {
            return NextResponse.json({ error: "Missing 'code' parameter" }, { status: 400 });
        }

        const result = await GetGlobalSetting(code);

        return NextResponse.json({ result }, { status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Update "Value"
export async function POST(request: NextRequest) {
    try {

        const orderData:GlobalSettingsModel = await request.json();

        await UpdateGlobalSettingsValue(orderData.Code, orderData.Value);

        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}