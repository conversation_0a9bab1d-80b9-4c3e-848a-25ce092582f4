import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class PaymentStatusModel extends _Base implements _ModelRequirements {

    static tableName: string = "PaymentStatus";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Code";
    static primaryDisplayField: string = "Name";
    static cacheMinutes_get: number = 30;
    static cacheMinutes_getList: number = 5;

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(code: string) {
        return await JC_Get<PaymentStatusModel>(PaymentStatusModel, this.apiRoute, { code });
    }
    static async ItemExists(code: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { code });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<PaymentStatusModel>(PaymentStatusModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: PaymentStatusModel) {
        return await JC_Put<PaymentStatusModel>(PaymentStatusModel, this.apiRoute, data);
    }
    static async CreateList(dataList: PaymentStatusModel[]) {
        return await JC_PutRaw<PaymentStatusModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "PaymentStatus");
    }
    static async Update(data: PaymentStatusModel) {
        return await JC_Post<PaymentStatusModel>(PaymentStatusModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: PaymentStatusModel[]) {
        return await JC_PostRaw<PaymentStatusModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "PaymentStatus");
    }
    static async Delete(code: string) {
        return await JC_Delete(PaymentStatusModel, this.apiRoute, code);
    }
    static async DeleteList(codes: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { codes }, undefined, "PaymentStatus");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Code: string;
    Name: string;
    Description: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<PaymentStatusModel>) {
        super(init);
        this.Code = "";
        this.Name = "";
        this.Description = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new PaymentStatusModel());
    }

    static jcFieldTypeforField(fieldName: keyof PaymentStatusModel) {
        switch (fieldName) {
            case "Code":
                return FieldTypeEnum.Text;
            case "Name":
                return FieldTypeEnum.Text;
            case "Description":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Name;
    }
}