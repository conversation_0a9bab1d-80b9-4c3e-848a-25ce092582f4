import { JC_Utils, JC_Utils_YtMusic } from "@/app/Utils";
import { ArtistItemModel } from "@/app/models/ArtistItem";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";


// Get items (could be Albums, Singles, Playlists etc.)
export const YT_GetItems = async function (bodyData:object, type:YtMusicItemTypeEnum, authorization: string, cookie: string):Promise<any[]> {

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        ...bodyData
    };

    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    try {

        console.log(JSON.stringify(res));

        let allitemsData = [];

        let itemsData = JC_Utils.findKeyValue(res.contents, "gridRenderer");
        allitemsData.push(...itemsData.items);

        for (var c of itemsData.continuations ?? []) {
            allitemsData.push(...(await GetContinuedItems(c, authorization, cookie)));
        };

        let allItems:ArtistItemModel[] = allitemsData.map(s => {
            let subtitle = JC_Utils.findKeyValue(s, "subtitle");
            let theYear = subtitle?.runs != null ? subtitle.runs.pop().text : null;
            return new ArtistItemModel({
                Id: JC_Utils.findKeyValue(s, "browseId"),
                PlaylistId: JC_Utils.findKeyValue(JC_Utils.findKeyValue(s, "thumbnailOverlay"), "playlistId"),
                Type: type,
                Title: s.musicTwoRowItemRenderer.title.runs[0].text,
                Year: !isNaN(theYear) ? Number(theYear) : null,
                ImageUrl: JC_Utils.findKeyValue(s, "thumbnails").pop().url
            });
        });

        return allItems;
    } catch (e) {
        let resString = JSON.stringify(res);
        console.log(resString);
        throw JSON.stringify(resString);
    }

}


// Get continued items
const GetContinuedItems = async function (continuation:any, authorization: string = '', cookie: string = ''):Promise<any> {

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext()
    };

    const cRes = (await (await fetch(`https://music.youtube.com/youtubei/v1/browse?ctoken=${continuation.nextContinuationData.continuation}&continuation=${continuation.nextContinuationData.continuation}&itct=${continuation.nextContinuationData.clickTrackingParams}&type=next&&prettyPrint=false`, {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    let cItems = [];

    let cItemsData = cRes.continuationContents.gridContinuation;
    cItems.push(...cItemsData.items);

    for (var c of cItemsData.continuations ?? []) {
        cItems.push(...(await GetContinuedItems(c, authorization, cookie)));
    };

    return cItems;

}