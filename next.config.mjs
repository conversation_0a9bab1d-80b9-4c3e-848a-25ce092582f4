/** @type {import('next').NextConfig} */
const nextConfig = {
    reactStrictMode: false,
    env: {
        apiBaseUrl: process.env.API_BASE_URL
    },
    images: {
        remotePatterns: [
            {
                protocol: 'https',
                hostname: 'lh3.googleusercontent.com',
                port: ''
            },
            {
                protocol: 'https',
                hostname: 'www.gstatic.com',
                port: ''
            },
            {
                protocol: 'https',
                hostname: 'yt3.ggpht.com',
                port: ''
            },
            {
                protocol: 'https',
                hostname: 'yt3.googleusercontent.com',
                port: ''
            },
            {
                protocol: 'https',
                hostname: 'i.ytimg.com',
                port: ''
            },
            {
                protocol: 'https',
                hostname: '*.public.blob.vercel-storage.com',
                port: ''
            },
        ],
    },
};

export default nextConfig;