import { sql } from "@vercel/postgres";
import { UserArtistSubscriptionModel } from "@/app/models/UserArtistSubscription";


// - GET - //

export async function GetUserArtistSubscription(id:string):Promise<UserArtistSubscriptionModel> {
    let dbUserArtistSubscription:UserArtistSubscriptionModel = (await sql<UserArtistSubscriptionModel>`
        SELECT "Id",
               "UserId",
               "ArtistId",
               "CheckedAlbumIdListJson",
               "CheckedSingleIdListJson",
               "CreatedAt",
               "ModifiedAt",
               "Deleted"
        FROM public."UserArtistSubscription"
        WHERE "Id" = ${id}
    `).rows[0];
    if (dbUserArtistSubscription?.CheckedAlbumIdListJson != null) {
        dbUserArtistSubscription.CheckedAlbumIdList = JSON.parse(dbUserArtistSubscription.CheckedAlbumIdListJson);
    }
    if (dbUserArtistSubscription?.CheckedSingleIdListJson != null) {
        dbUserArtistSubscription.CheckedSingleIdList = JSON.parse(dbUserArtistSubscription.CheckedSingleIdListJson);
    }
    return dbUserArtistSubscription;
}

export async function GetUserArtistSubscriptionList(userId:string):Promise<UserArtistSubscriptionModel[]> {
    let dbUserArtistSubscriptions:UserArtistSubscriptionModel[] = (await sql<UserArtistSubscriptionModel>`
        SELECT "Id",
               "UserId",
               "ArtistId",
               "ArtistName",
               "CheckedAlbumIdListJson",
               "CheckedSingleIdListJson",
               "CreatedAt",
               "ModifiedAt",
               "Deleted"
        FROM public."UserArtistSubscription"
        WHERE "UserId" = ${userId}
        ORDER BY "ArtistName"
    `).rows;
    dbUserArtistSubscriptions.forEach(sub => {
        if (sub?.CheckedAlbumIdListJson != null) {
            sub.CheckedAlbumIdList = JSON.parse(sub.CheckedAlbumIdListJson);
        }
        if (sub?.CheckedSingleIdListJson != null) {
            sub.CheckedSingleIdList = JSON.parse(sub.CheckedSingleIdListJson);
        }
    });
    return dbUserArtistSubscriptions;
}


// - CREATE - //

export async function CreateUserArtistSubscription(userArtistSubscriptionData:UserArtistSubscriptionModel) {
    await sql`
        INSERT INTO public."UserArtistSubscription"
        (
            "Id",
            "UserId",
            "ArtistId",
            "ArtistName",
            "CheckedAlbumIdListJson",
            "CheckedSingleIdListJson",
            "CreatedAt"
        )
        VALUES
        (
            ${userArtistSubscriptionData.Id},
            ${userArtistSubscriptionData.UserId},
            ${userArtistSubscriptionData.ArtistId},
            ${userArtistSubscriptionData.ArtistName},
            ${userArtistSubscriptionData.CheckedAlbumIdListJson},
            ${userArtistSubscriptionData.CheckedSingleIdListJson},
            ${new Date().toUTCString()}
        )
    `;
}


// - UPDATE - //

export async function UpdateUserArtistSubscription(userArtistSubscriptionData:UserArtistSubscriptionModel) {
    await sql`
        UPDATE public."UserArtistSubscription"
        SET "UserId"                  = ${userArtistSubscriptionData.UserId},
            "ArtistId"                = ${userArtistSubscriptionData.ArtistId},
            "ArtistName"              = ${userArtistSubscriptionData.ArtistName},
            "CheckedAlbumIdListJson"  = ${userArtistSubscriptionData.CheckedAlbumIdListJson},
            "CheckedSingleIdListJson" = ${userArtistSubscriptionData.CheckedSingleIdListJson},
            "ModifiedAt"              = ${new Date().toUTCString()},
            "Deleted"                 = ${userArtistSubscriptionData.Deleted}
        WHERE "Id" = ${userArtistSubscriptionData.Id}
    `;
}