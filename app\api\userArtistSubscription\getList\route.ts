import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { GetUserArtistSubscriptionList } from "../business";
import { UserArtistSubscriptionModel } from "@/app/models/UserArtistSubscription";

// Get
export async function GET(request: NextRequest) {

    try {
        unstable_noStore();
        const userId = new URL(request.url).searchParams.get("userId") as string;
        const result:UserArtistSubscriptionModel[] = await GetUserArtistSubscriptionList(userId);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}