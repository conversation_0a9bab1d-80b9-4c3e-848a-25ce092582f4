import { sql } from "@vercel/postgres";
import { GlobalSettingsModel } from "@/app/models/GlobalSettings";

// - GET - //

export async function GetGlobalSetting(code:string) {
    return (await sql<GlobalSettingsModel>`
        SELECT "Code",
               "Description",
               "Value"
        FROM public."GlobalSettings"
        WHERE "Code" = ${code}
    `).rows[0];
}


// - UPDATE - //

export async function UpdateGlobalSettingsValue(code:string, value:string) {
    await sql`
        UPDATE public."GlobalSettings"
        SET "Value" = ${value}
        WHERE "Code" = ${code}
    `;
}