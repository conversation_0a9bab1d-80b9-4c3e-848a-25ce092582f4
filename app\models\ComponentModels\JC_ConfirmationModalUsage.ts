export class JC_ConfirmationModalUsageModel {
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    width?:string;
    title:string;
    text:string;
    submitButtons: { text:string; onSubmit:()=>void; }[];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<JC_ConfirmationModalUsageModel>) {
        this.width = undefined;
        this.title = "";
        this.text = "";
        this.submitButtons = [];
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.title;
    }
}