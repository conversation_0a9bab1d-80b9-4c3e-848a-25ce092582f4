import { JC_Utils, JC_Utils_YtMusic } from '@/app/Utils';
import { SongModel } from '@/app/models/Song';
import { ReplaceCachedImageUrls } from "../imageCache/business";

// Custom error for unauthorized access
export class YtMusicUnauthorizedError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'YtMusicUnauthorizedError';
    }
}

export const YT_GetLastSongFromHistory = async function (authorization: string, cookie: string) : Promise<SongModel> {

    // Setup call
    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        "browseId": "FEmusic_history"
    }

    // Call with ytMusicHeaders to ensure proper headers
    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    // Check if the response indicates unauthorized access
    // Look for signs of unauthorized access in a more flexible way

    // Method 1: Check for the "Sign in to view your history" message anywhere in the response
    const responseStr = JSON.stringify(res);
    if (responseStr.includes("Sign in to view your history")) {
        throw new YtMusicUnauthorizedError("YouTube Music authentication failed. Please reset your token.");
    }

    // Method 2: Check if the musicResponsiveListItemRenderer is missing (which would be present in authorized responses)
    const hasListItems = JC_Utils.findKeyValue(res.contents, "musicResponsiveListItemRenderer");
    if (!hasListItems && responseStr.includes("Sign in")) {
        throw new YtMusicUnauthorizedError("YouTube Music authentication failed. Please reset your token.");
    }

    // Get last Song out of json response
    let lastItemData = JC_Utils.findKeyValue(res.contents, "musicResponsiveListItemRenderer");
    let song = new SongModel({
        Id: JC_Utils.findKeyValue(lastItemData, "videoId"),
        Title: JC_Utils.findKeyValue(lastItemData, "flexColumns")[0].musicResponsiveListItemFlexColumnRenderer.text.runs[0].text,
        Artist: JC_Utils.findKeyValue(lastItemData, "flexColumns")[1].musicResponsiveListItemFlexColumnRenderer.text.runs[0].text,
        ImageUrl: JC_Utils.findKeyValue(lastItemData, "thumbnails").pop().url
    });

    // Replace ImageUrl with cached blob URL if available
    const cachedSongs = await ReplaceCachedImageUrls([song]);

    // Return
    return cachedSongs[0];
}