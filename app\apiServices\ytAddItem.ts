import { JC_PostRaw } from "./JC_PostRaw";
import { LocalStorageKeyEnum } from "../enums/LocalStorageKey";

const resetIntervalMins = 30;

export async function YtAddItem(playlistUrl:string, itemId:string) {
    try {
        // Call API
        await JC_PostRaw("ytAddArtistItemToPlaylist", {
            playlistUrl: playlistUrl,
            itemId: itemId
        });
    } catch (error) {
        // IF timed out, just do call again, this one will be quicker since songs already added will have quick calls
        console.log("Error! Trying again...", error);
        await YtAddItem(playlistUrl, itemId);
    }
}