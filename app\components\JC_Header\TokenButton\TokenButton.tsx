"use client";

import React, { useState, useEffect } from 'react';
import Image from 'next/image';
import styles from './TokenButton.module.scss';
import ResetTokenModal from '../ResetTokenModal/ResetTokenModal';

export default function TokenButton() {
    const [isModalOpen, setIsModalOpen] = useState(false);

    // Listen for custom event to open the token reset modal
    useEffect(() => {
        const handleOpenTokenResetModal = () => {
            setIsModalOpen(true);
        };

        // Add event listener
        document.addEventListener('openTokenResetModal', handleOpenTokenResetModal);

        // Clean up
        return () => {
            document.removeEventListener('openTokenResetModal', handleOpenTokenResetModal);
        };
    }, []);

    return (
        <>
            <button
                className={styles.tokenButton}
                onClick={() => setIsModalOpen(true)}
                title="Reset YouTube Music Token"
            >
                <Image
                    src="/icons/Badge.png"
                    width={30}
                    height={30}
                    alt="Reset Token"
                />
            </button>

            <ResetTokenModal
                isOpen={isModalOpen}
                onClose={() => setIsModalOpen(false)}
            />
        </>
    );
}
