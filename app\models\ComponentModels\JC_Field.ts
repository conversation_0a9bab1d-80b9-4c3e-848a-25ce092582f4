import { JC_Utils, JC_Utils_Validation } from "@/app/Utils";
import { FieldTypeEnum } from "@/app/enums/FieldType";

export class JC_FieldModel {
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    overrideClass?: string;
    inputOverrideClass?: string;
    inputId: string;
    type: FieldTypeEnum;
    label?: string;
    iconName?: string;
    placeholder?: string;
    readOnly?: boolean;
    richTextEnableColor?: boolean;
    richTextEnableBold?: boolean;
    richTextEnableItalic?: boolean;
    value?: string | number; // This only works if an "inputId" is supplied
    defaultValue?: string | number;
    onClick?: () => void;
    onFocus?: () => void;
    onChange?: (newValue:string) => void;
    onBlur?: (newValue:string) => void;
    onEnter?: (event:any) => void;
    onEscape?: (event:any) => void;
    validate?: (value:string|number|undefined) => string; // On "JC_Form", only runs when submit clicked
    immediateValidate?: (value:string|number|undefined) => string; // On "JC_Form", runs no matter what
    required?: boolean;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<JC_FieldModel>) {
        this.overrideClass = undefined;
        this.inputOverrideClass = undefined;
        this.inputId = "";
        this.type = FieldTypeEnum.Text;
        this.label = undefined;
        this.iconName = undefined;
        this.placeholder = undefined;
        this.readOnly = false;
        this.richTextEnableColor = false;
        this.richTextEnableBold = false;
        this.richTextEnableItalic = false;
        this.value = undefined;
        this.defaultValue = undefined;
        this.onClick = undefined;
        this.onFocus = undefined;
        this.onChange = undefined;
        this.onBlur = undefined;
        this.onEnter = undefined;
        this.onEscape = undefined;
        this.validate = undefined;
        this.immediateValidate = undefined;
        this.required = false;
        Object.assign(this, init);
    }
}



// - Defaults For Specific Fields - //

// First Name
export function D_FieldModel_FirstName():JC_FieldModel {
    return new JC_FieldModel({
        inputId: "first-name-input",
        type: FieldTypeEnum.Text,
        label: "First Name",
        iconName: "User",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a first name." : ""
    });
}

// Last Name
export function D_FieldModel_LastName():JC_FieldModel {
    return new JC_FieldModel({
        inputId: "last-name-input",
        type: FieldTypeEnum.Text,
        label: "Last Name",
        iconName: "User",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v) ? "Enter a last name." : ""
    });
}

// Email
export function D_FieldModel_Email():JC_FieldModel {
    return new JC_FieldModel({
        inputId: "email-input",
        type: FieldTypeEnum.Email,
        label: "Email",
        iconName: "Email",
        validate: (v:any) => JC_Utils.stringNullOrEmpty(v)
                                ? "Enter an email."
                                : !JC_Utils_Validation.validEmail(v)
                                    ? "Enter a valid email"
                                    : ""
    });
}

// Phone
export function D_FieldModel_Phone():JC_FieldModel {
    return new JC_FieldModel({
        inputId: "contact-email-input",
        type: FieldTypeEnum.Text,
        label: "Phone (optional)",
        iconName: "Phone",
        validate: (v:any) => !JC_Utils.stringNullOrEmpty(v) && !JC_Utils_Validation.validPhone(v) ? "Enter a valid phone number" : ""
    });
}