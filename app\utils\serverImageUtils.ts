import sharp from 'sharp';

/**
 * Compress image buffer to target size in KB using Sharp
 * @param buffer - Original image buffer
 * @param targetKb - Target size in kilobytes
 * @returns Compressed image buffer
 */
export async function compressImageToSize(buffer: Buffer, targetKb: number): Promise<Buffer> {
    const targetSizeBytes = targetKb * 1024;
    let quality = 90; // Start with high quality
    let compressedBuffer = buffer;
    
    // Try different quality levels until we get under the target size
    while (quality >= 10) {
        try {
            compressedBuffer = await sharp(buffer)
                .jpeg({ quality })
                .toBuffer();
            
            // If the compressed image is under the target size, use it
            if (compressedBuffer.length <= targetSizeBytes) {
                return compressedBuffer;
            }
            
            // Reduce quality for next iteration
            quality -= 10;
        } catch (error) {
            console.warn(`Failed to compress image at quality ${quality}:`, error);
            break;
        }
    }
    
    // If still too large, try resizing the image
    if (compressedBuffer.length > targetSizeBytes && quality < 10) {
        let width = 800; // Start with reasonable width
        
        while (width >= 200) {
            try {
                compressedBuffer = await sharp(buffer)
                    .resize(width, null, { withoutEnlargement: true })
                    .jpeg({ quality: 70 })
                    .toBuffer();
                
                if (compressedBuffer.length <= targetSizeBytes) {
                    return compressedBuffer;
                }
                
                width -= 100;
            } catch (error) {
                console.warn(`Failed to resize image to width ${width}:`, error);
                break;
            }
        }
    }
    
    // Return the best we could achieve
    return compressedBuffer;
}

/**
 * Generate Base64 from URL with optional compression
 * @param url - Image URL
 * @param qualityKb - Optional target size in KB
 * @returns Base64 string
 */
export async function generateBase64FromUrl(url: string, qualityKb?: number): Promise<string> {
    const imageRes = await fetch(url);
    if (!imageRes.ok) {
        throw new Error(`Failed to fetch image for Base64 conversion (${imageRes.status})`);
    }
    let buffer = Buffer.from(await imageRes.arrayBuffer());
    
    // If qualityKb is specified, compress the image to meet the size requirement
    if (qualityKb && qualityKb > 0) {
        buffer = await compressImageToSize(buffer, qualityKb);
    }
    
    return buffer.toString('base64');
}
