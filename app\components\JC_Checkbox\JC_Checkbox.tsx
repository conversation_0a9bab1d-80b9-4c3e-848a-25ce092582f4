"use client"

import styles from "./JC_Checkbox.module.scss";
import React from 'react';
import { JC_Utils } from '@/app/Utils';

export default function JC_Checkbox(_: Readonly<{

    label?: string;
    checked?: boolean;
    onChange: () => void;

}>) {

    return (
        <div className={styles.mainContainer} onClick={_.onChange}>
            <div className={styles.checkbox}>
                {_.checked && <div className={styles.innerCheckedSquare} />}
            </div>
            {!JC_Utils.stringNullOrEmpty(_.label) && <label>{_.label}</label>}
        </div>
    );
}