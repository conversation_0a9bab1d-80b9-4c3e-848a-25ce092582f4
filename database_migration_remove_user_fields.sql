-- Migration script to remove unused User fields
-- This removes LoginFailedAttempts, LoginLockoutDate, ChangePasswordToken, ChangePasswordTokenDate, Phone, IsWholesale, CompanyName from User table

-- Drop the columns from the User table
ALTER TABLE public."User" 
DROP COLUMN IF EXISTS "LoginFailedAttempts",
DROP COLUMN IF EXISTS "LoginLockoutDate",
DROP COLUMN IF EXISTS "ChangePasswordToken",
DROP COLUMN IF EXISTS "ChangePasswordTokenDate",
DROP COLUMN IF EXISTS "Phone",
DROP COLUMN IF EXISTS "IsWholesale",
DROP COLUMN IF EXISTS "CompanyName";

-- Add comments to document the changes
COMMENT ON TABLE public."User" IS 'User table - removed unused authentication and profile fields in cleanup migration';

-- Note: This migration removes fields that are no longer used:
-- - LoginFailedAttempts: Login attempt tracking removed (no longer using password auth)
-- - LoginLockoutDate: User lockout functionality removed
-- - ChangePasswordToken: Password reset functionality removed
-- - ChangePasswordTokenDate: Password reset functionality removed  
-- - Phone: Phone number field removed from user profile
-- - IsWholesale: Wholesale user functionality removed
-- - CompanyName: Company name field removed (was for wholesale users)
