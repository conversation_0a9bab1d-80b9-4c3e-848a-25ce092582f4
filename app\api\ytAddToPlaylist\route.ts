import { NextRequest, NextResponse } from "next/server";
import { YT_AddToPlaylist } from "./business";
import { SongModel } from "@/app/models/Song";
import { auth } from "@/app/auth";
import { GetUserYtMusicTokens } from "../user/business";


// Update
export async function POST(request: NextRequest) {
    try {
        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get request data
        const requestData:{ playlistUrl:string, song:SongModel } = await request.json();

        // Validate required parameters
        if (!requestData.playlistUrl || !requestData.song) {
            return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
        }

        // Get user's YT Music tokens
        const { authToken, cookie } = await GetUserYtMusicTokens(session.user.id);

        // Call business logic with auth credentials
        await YT_AddToPlaylist(
            requestData.playlistUrl,
            [requestData.song],
            authToken,
            cookie
        );

        return NextResponse.json({ status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}