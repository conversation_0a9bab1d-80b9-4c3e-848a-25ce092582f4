@import '../../global';

.mainContainer {
    outline: solid 2px $offBlack;
    width: 240px;
    height: 255px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    border-radius: $smallBorderRadius;
    overflow: hidden;
    cursor: pointer;

    &:hover {
        outline: solid $smallBorderWidth $primaryColor;
        font-weight: bold;
    }

    // Image
    .groupImage {
        height: 82%;
        width: 100%;
        object-fit: cover;
    }

    // Name
    .groupName {
        flex-grow: 1;
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        text-align: center;
        font-size: 20px;
    }
}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        width: 200px;
        height: 210px;
        .groupName {
            font-size: 17px;
        }
    }
}

@media (max-width: $tinyScreenSize) {
    .mainContainer {
        width: 150px;
        height: 160px;
        .groupName {
            font-size: 14px;
        }
    }
}