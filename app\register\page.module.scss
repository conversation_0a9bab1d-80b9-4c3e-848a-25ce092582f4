@import '../global';

.mainContainer {
    @include mainPageStyles;

    // Tab Body
    .tabBody {
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        row-gap: 20px;

        // Charlotte
        .registerCharlotte {
            position: absolute;
            height: auto;
            opacity: $charlotteOpacity;
            top: -10px;
            left: -240px;
            width: 185px;
        }

        // Password Requirements Field
        .passwordRequirementsField {
            margin-top: -15px;
            margin-bottom: -15px;
        }
    }

}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .registerCharlotte {
        display: none;
    }
}
