cls ^
&& echo DROP SCHEMA public CASCADE; > DbSchemaGenerated.sql ^
&& echo CREATE SCHEMA public; >> DbSchemaGenerated.sql ^

&& echo. >> DbSchemaGenerated.sql ^
&& echo. >> DbSchemaGenerated.sql ^
&& echo -- ------- -- >> DbSchemaGenerated.sql ^
&& echo -- CREATES -- >> DbSchemaGenerated.sql ^
&& echo -- ------- -- >> DbSchemaGenerated.sql ^
&& echo. >> DbSchemaGenerated.sql ^
&& dbml2sql DbSchema.dbml -o temp.txt ^
&& type temp.txt >> DbSchemaGenerated.sql ^

&& echo. >> DbSchemaGenerated.sql ^
&& echo. >> DbSchemaGenerated.sql ^
&& echo -- ------- -- >> DbSchemaGenerated.sql ^
&& echo -- INSERTS -- >> DbSchemaGenerated.sql ^
&& echo -- ------- -- >> DbSchemaGenerated.sql ^
&& type DbSchemaInserts.sql >> DbSchemaGenerated.sql ^

&& (type DbSchemaGenerated.sql | clip) ^
&& del DbSchemaGenerated.sql ^
&& del temp.txt ^
&& del -f dbml-error.log