import React from "react";
import type { <PERSON>ada<PERSON> } from "next";
import { redirect } from 'next/navigation'
import J<PERSON>_Header from "../components/JC_Header/JC_Header";
import { auth } from '../auth';

// Site Metadata
export const metadata: Metadata = {
    title: "YT Music Automator - Add Artist",
    description: "Add all of an artist's songs to your selected playlist."
};


export default async function Layout_AddAllArtist(_: Readonly<{

    children: React.ReactNode;

}>) {

    // - AUTH - //

    // const session = await auth();
    // if (!session) {
    //     redirect("/loginRegister");
    // }

    // - MAIN - //

    return <React.Fragment>
        <JC_Header />
        {_.children}
    </React.Fragment>;

}
