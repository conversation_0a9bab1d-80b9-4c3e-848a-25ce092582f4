import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class UserConfigModel extends _Base implements _ModelRequirements {

    static tableName: string = "UserConfig";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "UserId";
    static primaryDisplayField: string = "UserId";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(userId: string) {
        return await JC_Get<UserConfigModel>(UserConfigModel, this.apiRoute, { userId });
    }
    static async ItemExists(userId: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { userId });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<UserConfigModel>(UserConfigModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: UserConfigModel) {
        return await JC_Put<UserConfigModel>(UserConfigModel, this.apiRoute, data);
    }
    static async CreateList(dataList: UserConfigModel[]) {
        return await JC_PutRaw<UserConfigModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "UserConfig");
    }
    static async Update(data: UserConfigModel) {
        return await JC_Post<UserConfigModel>(UserConfigModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: UserConfigModel[]) {
        return await JC_PostRaw<UserConfigModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "UserConfig");
    }
    static async Delete(userId: string) {
        return await JC_Delete(UserConfigModel, this.apiRoute, userId);
    }
    static async DeleteList(userIds: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { userIds }, undefined, "UserConfig");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    UserId: string;
    NominatedToListenPlaylistUrl: string|null;
    SelectedPlaylistIdListJson: string|null;
    HiddenPlaylistIdListJson: string|null;
    NominatedNewSongsPlaylistUrl: string|null;
    NewSongsProcessDateLastRun: Date|null;

    // Extended
    SelectedPlaylistIdList?: string[];
    HiddenPlaylistIdList?: string[];

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserConfigModel>) {
        super(init);
        this.UserId = "";
        this.NominatedToListenPlaylistUrl = null;
        this.SelectedPlaylistIdListJson = null;
        this.HiddenPlaylistIdListJson = null;
        this.NominatedNewSongsPlaylistUrl = null;
        this.NewSongsProcessDateLastRun = null;
        this.SelectedPlaylistIdList = [];
        this.HiddenPlaylistIdList = [];
        Object.assign(this, init);

        // Convert date strings to Date objects if needed
        if (this.NewSongsProcessDateLastRun && typeof this.NewSongsProcessDateLastRun === 'string') {
            this.NewSongsProcessDateLastRun = new Date(this.NewSongsProcessDateLastRun);
        }
    }

    static getKeys() {
        return Object.keys(new UserConfigModel());
    }

    static jcFieldTypeforField(fieldName: keyof UserConfigModel) {
        switch (fieldName) {
            case "UserId":
                return FieldTypeEnum.Text;
            case "NominatedToListenPlaylistUrl":
                return FieldTypeEnum.Text;
            case "SelectedPlaylistIdListJson":
                return FieldTypeEnum.Text;
            case "HiddenPlaylistIdListJson":
                return FieldTypeEnum.Text;
            case "NominatedNewSongsPlaylistUrl":
                return FieldTypeEnum.Text;
            case "NewSongsProcessDateLastRun":
                return FieldTypeEnum.Date;
            default:
                return FieldTypeEnum.Text;
        }
    }
}

export function D_UserConfig(userId:string):UserConfigModel {
    return new UserConfigModel({ UserId: userId });
}