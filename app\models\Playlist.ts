import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class PlaylistModel extends _Base implements _ModelRequirements {

    static tableName: string = "Playlist";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Title";
    // static cacheMinutes_get: number = 30;
    // static cacheMinutes_getList: number = 5;

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<PlaylistModel>(PlaylistModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<PlaylistModel>(PlaylistModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: PlaylistModel) {
        return await JC_Put<PlaylistModel>(PlaylistModel, this.apiRoute, data);
    }
    static async CreateList(dataList: PlaylistModel[]) {
        return await JC_PutRaw<PlaylistModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "Playlist");
    }
    static async Update(data: PlaylistModel) {
        return await JC_Post<PlaylistModel>(PlaylistModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: PlaylistModel[]) {
        return await JC_PostRaw<PlaylistModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "Playlist");
    }
    static async Delete(id: string) {
        return await JC_Delete(PlaylistModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "Playlist");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    Title: string;
    ImageUrl: string;

    // UI
    IsSelected?: boolean;
    IsHidden?: boolean;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<PlaylistModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.Title = "";
        this.ImageUrl = "";
        this.IsSelected = false;
        this.IsHidden = false;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new PlaylistModel());
    }

    static jcFieldTypeforField(fieldName: keyof PlaylistModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "Title":
                return FieldTypeEnum.Text;
            case "ImageUrl":
                return FieldTypeEnum.Text;
            case "IsSelected":
                return FieldTypeEnum.Text;
            case "IsHidden":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Title;
    }
}