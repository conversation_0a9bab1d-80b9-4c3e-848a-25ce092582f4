import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { CreateUserProcessProgress, GetUserProcessProgress, UpdateUserProcessProgress } from "./business";
import { UserProcessProgressModel } from "@/app/models/UserProcessProgress";
import { UserProcessProgressCodeEnum } from "@/app/enums/UserProcessProgressCode";

// Get
export async function GET(request: NextRequest) {

    try {
        unstable_noStore();
        const params = new URL(request.url).searchParams;
        const userId = params.get("userId") as string;
        const processCode = params.get("processCode") as UserProcessProgressCodeEnum;
        const result:UserProcessProgressModel = await GetUserProcessProgress(userId, processCode);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Create
export async function PUT(request: NextRequest) {
    try {

        const dbUserProcessProgress:UserProcessProgressModel = await request.json();
        await CreateUserProcessProgress(dbUserProcessProgress);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Update
export async function POST(request: NextRequest) {
    try {

        const dbUserProcessProgress:UserProcessProgressModel = await request.json();
        // First check if exists
        if ((await GetUserProcessProgress(dbUserProcessProgress.UserId, dbUserProcessProgress.ProcessCode)) == null) {
            await CreateUserProcessProgress(dbUserProcessProgress);
        } else {
            await UpdateUserProcessProgress(dbUserProcessProgress);
        }
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}