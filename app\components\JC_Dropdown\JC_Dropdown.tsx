"use client"

import styles from "./JC_Dropdown.module.scss";
import React, { useState } from 'react';
import Image from "next/image";
import JC_Checkbox from '../JC_Checkbox/JC_Checkbox';
import { JC_Utils } from '@/app/Utils';
import { JC_DropdownOptionModel } from '@/app/models/ComponentModels/JC_DropdownOption';
import { DropdownTypeEnum } from '@/app/enums/DropdownType';

export default function JC_Dropdown(_: Readonly<{
    overrideClass?: string;
    type: DropdownTypeEnum;
    label?: string;
    placeholder?: string;
    options: JC_DropdownOptionModel[];
    onOptionMouseOver?: (optionId:string) => void;
    onOptionMouseOut?: (optionId:string) => void;
    selectedOptionId?: string;
    removeSelectedInDropdown?: boolean;
    enableSearch?: boolean;
    onSelection: (newOptionId:string) => void;
}>) {

    // - STATE - //

    const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
    const [searchBoxText, setSearchBoxText] = useState<string>();
    const [selectedOption, setSelectedOption] = useState<JC_DropdownOptionModel | undefined>(
        !JC_Utils.stringNullOrEmpty(_.selectedOptionId) ? _.options.find(x => x.OptionId == _.selectedOptionId)! : undefined
    );
    const [selectedOptions, setSelectedOptions] = useState<string[]>([]);


    // - INITIALISE - //

    let optionsList = _.options;
    if (_.removeSelectedInDropdown) {
        optionsList = optionsList.filter(o => o.OptionId != selectedOption?.OptionId);
    }
    if (!JC_Utils.stringNullOrEmpty(searchBoxText)) {
        optionsList = optionsList.filter(o => JC_Utils.searchMatches(searchBoxText!, o.Label));
    }


    // - BUILD - //

    // Dropdown Option Content
    function buildOptionContent(option?:JC_DropdownOptionModel, isMain?:boolean) {
        return <React.Fragment>

            {!JC_Utils.stringNullOrEmpty(option?.IconName) &&
            <Image
                src={`/icons/${option!.IconName}.svg`}
                width={100}
                height={100}
                className={styles.optionIcon}
                alt="Bag"
            />}

            {!JC_Utils.stringNullOrEmpty(option?.Label) &&
            <div className={styles.optionLabel}>{option!.Label}</div>}

            {option == null && !JC_Utils.stringNullOrEmpty(_.placeholder) &&
            <div className={styles.optionLabel}>{_.placeholder}</div>}

            {_.type == DropdownTypeEnum.Multi && !isMain && option != null &&
            <div className={styles.checkbox}>
                <JC_Checkbox
                    checked={option.Selected ?? false}
                    onChange={() => _.onSelection(option.OptionId)}
                />
            </div>}

        </React.Fragment>
    }


    // - HANDLES - //

    // Option selection
    function selectOption(optionId:string) {
        _.onSelection(optionId);
        setSearchBoxText("");
        if (_.type == DropdownTypeEnum.Default) {
            setDropdownOpen(false);
        }
    }

    function defaultIsOptionSelected(optionId:string) {
        return _.type == DropdownTypeEnum.Default && optionId == selectedOption?.OptionId;
    }


    // - MAIN - //

    return (
        <div className={`${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            {/* Label */}
            {!JC_Utils.stringNullOrEmpty(_.label) && <div className={styles.label}>{_.label}</div>}

            {/* Dropdown */}
            <div className={styles.mainContainer}>

                {/* Outside Click Div */}
                {dropdownOpen && <div className={styles.outsideClickDiv} onClick={() => setDropdownOpen(false)} />}

                {/* Selected Option */}
                <div
                    className={`${styles.mainButton} ck-dropdown`}
                    onClick={() => {
                        if (!dropdownOpen) {
                            setDropdownOpen(true);
                        } else {
                            setSearchBoxText("");
                            setDropdownOpen(false);
                        }
                    }}
                >

                    {/* Selected Option */}
                    {buildOptionContent(selectedOption, true)}

                    {/* Chevron */}
                    <Image
                        className={styles.chevronIcon}
                        src="/icons/Chevron.svg"
                        style={dropdownOpen ? {rotate: "180deg", top: "20%"} : {}}
                        width={50}
                        height={50}
                        alt="Bag"
                    />

                    {/* Search */}
                    {_.enableSearch && dropdownOpen &&
                    <input className={styles.searchBox} type="text" placeholder="Search..." onChange={(event) => setSearchBoxText(event.target.value)} />}

                </div>

                {/* Dropdown List */}
                {dropdownOpen &&
                <div
                    className={styles.dropdown}
                    style={dropdownOpen ? { zIndex: 99 } : {}}
                >
                    {optionsList.map(option =>
                        <div
                            key={option.OptionId}
                            className={`${styles.dropdownOption} ${defaultIsOptionSelected(option.OptionId) ? styles.selected : ''}`}
                            onClick={() => !defaultIsOptionSelected(option.OptionId) ? selectOption(option.OptionId) : null}
                            onMouseOver={() => _.onOptionMouseOver != null ? _.onOptionMouseOver(option.OptionId) : null}
                            onMouseOut={() => _.onOptionMouseOut != null ? _.onOptionMouseOut(option.OptionId) : null}
                        >
                            {buildOptionContent(option)}
                        </div>
                    )}
                </div>}
                {/* {dropdownOpen && options.length > 8 && <div className={styles.bottomFade} id="bottomFade"/>} */}

            </div>

        </div>
    );
}