import { SongModel } from "@/app/models/Song";
import { JC_Utils, JC_Utils_YtMusic } from "@/app/Utils";

export const YT_AddToPlaylist = async function (playlistUrl:string, songs:SongModel[], authorization: string, cookie: string) {

    let playlistId:string = JC_Utils_YtMusic.getPlaylistIdFromUrl(playlistUrl).substring(2);

    for (let song of songs) {

        console.log(`SongId: ${song.Id}`)

        // Setup call
        const body = {
            "context": JC_Utils_YtMusic.ytMusicContext(),
            "actions": [{
                "action": "ACTION_ADD_VIDEO",
                "addedVideoId": song.Id,
                "dedupeOption": "DEDUPE_OPTION_CHECK"
            }],
            "playlistId": playlistId
        }

        // Call
        try {
            await fetch(`https://music.youtube.com/youtubei/v1/browse/edit_playlist?prettyPrint=false`, {
                method: 'POST',
                headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
                body: JSON.stringify(body)
            });
        } catch (err) {
            // Try once more
            try {
                await fetch(`https://music.youtube.com/youtubei/v1/browse/edit_playlist?prettyPrint=false`, {
                    method: 'POST',
                    headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
                    body: JSON.stringify(body)
                });
            } catch (error) {
                // <>WWW<> SHOW ERROR
                console.log("Error!");
            }
        }
    }
    // Wait a bit before adding next item to give time for YT to finish adding last items
    await JC_Utils.sleep(0.5);

}