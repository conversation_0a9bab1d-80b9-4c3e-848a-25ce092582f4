@import '../../global';

// Header
.mainContainer {
    position: relative;
    width: max-content;
    height: max-content;
    padding: 30px 30px;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    row-gap: 30px;
    border: solid 6px $offBlack;
    outline: solid $largeBorderWidth $primaryColor;
    border-radius: $largeBorderRadius;
    background-color: $primaryColor;

    // Logo
    .logo {
        margin-top: -10px;
        width: 180px;
        height: auto;
        max-height: 400px;
        box-sizing: border-box;
        user-select: none;
        &:hover {
            opacity: 1;
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $mediumScreenSize) {
}
@media (max-width: $smallScreenSize) {
}

@media (max-width: $tinyScreenSize) {
}

@media (max-width: $teenyTinyScreenSize) {
}