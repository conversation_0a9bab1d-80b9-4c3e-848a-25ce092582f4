import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { YT_GetLastSongFromHistory, YtMusicUnauthorizedError } from "./business";
import { auth } from "@/app/auth";
import { GetUserYtMusicTokens } from "../user/business";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get user's YT Music tokens
        const { authToken, cookie } = await GetUserYtMusicTokens(session.user.id);

        // Call business logic with auth credentials
        const result = await YT_GetLastSongFromHistory(authToken, cookie);

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);

        // Check if this is an unauthorized error
        if (error instanceof YtMusicUnauthorizedError) {
            return NextResponse.json({
                error: error.message,
                unauthorized: true
            }, { status: 401 });
        }

        return NextResponse.json({ error }, { status: 500 });
    }
}