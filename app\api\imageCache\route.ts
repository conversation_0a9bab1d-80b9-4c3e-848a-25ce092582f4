import { NextRequest, NextResponse } from "next/server";
import { StoreImage, ReplaceCachedImageUrls } from "./business";
import { ImageCacheTypeEnum } from "../../enums/ImageCacheType";

export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url);
        const originalUrl = searchParams.get('url');
        const cacheTypeParam = searchParams.get('cacheType');

        if (!originalUrl) {
            return NextResponse.json({ error: 'Missing url parameter' }, { status: 400 });
        }

        // Parse cacheType parameter, default to Base64 as requested
        let cacheType = ImageCacheTypeEnum.Base64;
        if (cacheTypeParam === 'BlobStorage') {
            cacheType = ImageCacheTypeEnum.BlobStorage;
        }

        const result = await StoreImage(originalUrl, cacheType);
        return NextResponse.json(result);
    } catch (error) {
        console.error('Store image API error:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Failed to store image' },
            { status: 500 }
        );
    }
}

export async function POST(request: NextRequest) {
    try {
        const items = await request.json();

        if (!Array.isArray(items)) {
            return NextResponse.json({ error: 'Request body must be an array of items' }, { status: 400 });
        }

        const result = await ReplaceCachedImageUrls(items);
        return NextResponse.json(result);
    } catch (error) {
        console.error('Replace cached image URLs API error:', error);
        return NextResponse.json(
            { error: error instanceof Error ? error.message : 'Failed to replace cached image URLs' },
            { status: 500 }
        );
    }
}
