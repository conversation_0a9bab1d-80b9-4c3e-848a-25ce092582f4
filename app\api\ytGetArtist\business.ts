import { YT_GetItems } from "../ytGetItems/business";
import { JC_Utils, JC_Utils_YtMusic } from "@/app/Utils";
import { ArtistFullModel } from "@/app/models/Artist";
import { ArtistItemModel } from "@/app/models/ArtistItem";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";
import { ReplaceCachedImageUrls } from "../imageCache/business";

export async function YT_GetArtist(artistUrl:string, authorization: string, cookie: string):Promise<ArtistFullModel|null> {

    // - Get Artist response - //

    const artistId:string = artistUrl.split('/').pop()!;
    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        "browseId": artistId,
    };
    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());


    // - Get Albums - //

    let albums:ArtistItemModel[] = [];
    let findingAlbumsParamWhereCallback = function (obj:any) {
        return JC_Utils.findKeyValue(obj, "text").toLowerCase() == "albums";
    }
    let albumsObject = JC_Utils.findKeyValue(res.contents, "musicCarouselShelfRenderer", findingAlbumsParamWhereCallback);
    let albumsRuns = JC_Utils.findKeyValue(albumsObject, "runs");
    // IF there are any Albums
    if (albumsRuns != null && albumsRuns.length > 0) {
        let albumsParamsObject = albumsRuns[0].navigationEndpoint;
        // IF Albums does not have a 'More' button
        if (albumsParamsObject == null) {
            albums = albumsObject.contents.map((albumObject:any) => {
                let theYear = JC_Utils.findKeyValue(albumObject, "subtitle").runs.pop().text;
                return {
                    Id: JC_Utils.findKeyValue(albumObject, "browseId"),
                    PlaylistId: JC_Utils.findKeyValue(JC_Utils.findKeyValue(albumObject, "thumbnailOverlay"), "playlistId"),
                    Title: JC_Utils.findKeyValue(albumObject, "text"),
                    Year: !isNaN(theYear) ? Number(theYear) : null,
                    ImageUrl: JC_Utils.findKeyValue(albumObject, "thumbnails").pop().url
                } as ArtistItemModel
            });
        } else {
            albums = await YT_GetItems({
                "browseId":  albumsParamsObject.browseEndpoint.browseId,
                "params":  albumsParamsObject.browseEndpoint.params
            }, YtMusicItemTypeEnum.Album, authorization, cookie) as ArtistItemModel[];
        }
    }


    // - Get Singles - //

    let singles:ArtistItemModel[] = [];
    let findingSinglesParamWhereCallback = function (obj:any) {
        return JC_Utils.findKeyValue(obj, "text").toLowerCase().includes("singles");
    }
    let singlesObject = JC_Utils.findKeyValue(res.contents, "musicCarouselShelfRenderer", findingSinglesParamWhereCallback);
    let singlesRuns = JC_Utils.findKeyValue(singlesObject, "runs");
    // IF there are any Singles
    if (singlesRuns != null && singlesRuns.length > 0) {
        let singlesParamsObject = singlesRuns[0].navigationEndpoint;
        // IF Singles does not have a 'More' button
        if (singlesParamsObject == null) {
            singles = singlesObject.contents.map((singleObject:any) => {
                let theYear = JC_Utils.findKeyValue(singleObject, "subtitle").runs.pop().text;
                return {
                    Id: JC_Utils.findKeyValue(singleObject, "browseId"),
                    PlaylistId: JC_Utils.findKeyValue(JC_Utils.findKeyValue(singleObject, "thumbnailOverlay"), "playlistId"),
                    Title: JC_Utils.findKeyValue(singleObject, "text"),
                    Year: !isNaN(theYear) ? Number(theYear) : null,
                    ImageUrl: JC_Utils.findKeyValue(singleObject, "thumbnails").pop().url
                } as ArtistItemModel
            });
        } else {
            singles = await YT_GetItems({
                "browseId":  singlesParamsObject.browseEndpoint.browseId,
                "params":  singlesParamsObject.browseEndpoint.params
            }, YtMusicItemTypeEnum.Single, authorization, cookie) as ArtistItemModel[];
        }
    }

    if (res?.header?.musicImmersiveHeaderRenderer?.title?.runs[0]?.text == null) {
        return null;
    } else {
        let artist = new ArtistFullModel({
            Id: "", // <>WWW<>
            Name: res.header.musicImmersiveHeaderRenderer.title.runs[0].text, // <>WWW<> FIX THIS SHIT
            ImageUrl: JC_Utils.findKeyValue(res.header, "thumbnails")?.pop()?.url,
            Albums: albums.reverse().map(a => ({ ...a, Type: YtMusicItemTypeEnum.Album })),
            Singles: singles.reverse().map(s => ({ ...s, Type: YtMusicItemTypeEnum.Single }))
         });

        // Cache the artist image
        const cachedArtists = await ReplaceCachedImageUrls([artist]);
        return cachedArtists[0];
    }

}