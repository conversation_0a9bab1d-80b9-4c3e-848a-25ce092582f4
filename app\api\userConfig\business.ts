import { sql } from "@vercel/postgres";
import { JC_Utils_Dates } from "@/app/Utils";
import { UserConfigModel } from "../../models/UserConfig";


// - GET - //

export async function GetUserConfig(userId:string):Promise<UserConfigModel> {
    let dbUserConfigModel = (await sql<UserConfigModel>`
        SELECT "UserId",
               "NominatedToListenPlaylistUrl",
               "SelectedPlaylistIdListJson",
               "HiddenPlaylistIdListJson",
               "NominatedNewSongsPlaylistUrl",
               "NewSongsProcessDateLastRun",
               "CreatedAt",
               "ModifiedAt",
               "Deleted"
        FROM public."UserConfig"
        WHERE "UserId" = ${userId}
    `).rows[0];
    if (dbUserConfigModel?.SelectedPlaylistIdListJson != null) {
        dbUserConfigModel.SelectedPlaylistIdList = JSON.parse(dbUserConfigModel.SelectedPlaylistIdListJson);
    }
    if (dbUserConfigModel?.HiddenPlaylistIdListJson != null) {
        dbUserConfigModel.HiddenPlaylistIdList = JSON.parse(dbUserConfigModel.HiddenPlaylistIdListJson);
    }
    return dbUserConfigModel;
}


// - CREATE - //

export async function CreateUserConfig(userConfigData:UserConfigModel) {
    await sql`
        INSERT INTO public."UserConfig"
        (
            "UserId",
            "NominatedToListenPlaylistUrl",
            "SelectedPlaylistIdListJson",
            "HiddenPlaylistIdListJson",
            "NominatedNewSongsPlaylistUrl",
            "NewSongsProcessDateLastRun",
            "CreatedAt"
        )
        VALUES
        (
            ${userConfigData.UserId},
            ${userConfigData.NominatedToListenPlaylistUrl},
            ${userConfigData.SelectedPlaylistIdListJson},
            ${userConfigData.HiddenPlaylistIdListJson},
            ${userConfigData.NominatedNewSongsPlaylistUrl},
            ${userConfigData.NewSongsProcessDateLastRun != null ? JC_Utils_Dates.formatDateForPostgres(userConfigData.NewSongsProcessDateLastRun!) : null},
            ${new Date().toUTCString()}
        )
    `;
}


// - UPDATE - //

// Main
export async function UpdateUserConfig(userConfigData:UserConfigModel) {
    await sql`
        UPDATE public."UserConfig"
        SET "NominatedToListenPlaylistUrl" = ${userConfigData.NominatedToListenPlaylistUrl},
            "SelectedPlaylistIdListJson"   = ${userConfigData.SelectedPlaylistIdListJson},
            "HiddenPlaylistIdListJson"     = ${userConfigData.HiddenPlaylistIdListJson},
            "NominatedNewSongsPlaylistUrl" = ${userConfigData.NominatedNewSongsPlaylistUrl},
            "NewSongsProcessDateLastRun"   = ${JC_Utils_Dates.formatDateForPostgres(userConfigData.NewSongsProcessDateLastRun!)},
            "ModifiedAt"                   = ${new Date().toUTCString()},
            "Deleted"                      = ${userConfigData.Deleted}
        WHERE "UserId" = ${userConfigData.UserId}
    `;
}