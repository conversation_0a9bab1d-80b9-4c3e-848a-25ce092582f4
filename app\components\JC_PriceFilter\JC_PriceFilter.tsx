"use client"

import styles from "./JC_PriceFilter.module.scss";
import React, { useEffect, useState } from 'react';
import Image from "next/image";
import JC_Field from '../JC_Field/JC_Field';
import { JC_Utils } from '@/app/Utils';
import { JC_PriceFilterModel } from '@/app/models/ComponentModels/JC_PriceFilter';
import { FieldTypeEnum } from '@/app/enums/FieldType';

export default function JC_PriceFilter(_: Readonly<{
    overrideClass?: string;
    initialValues?: JC_PriceFilterModel;
    onChange: (newFilter: JC_PriceFilterModel) => void;
}>) {

    // - STATE - //

    const [dropdownOpen, setDropdownOpen] = useState<boolean>(false);
    const [mouseInsideMain, setMouseInsideMain] = useState<boolean>(false);
    const [mouseInsideDropdown, setMouseInsideDropdown] = useState<boolean>(false);
    const [mouseInsideToInput, setMouseInsideToInput] = useState<boolean>(false);
    const [fromPrice, setFromPrice] = useState<number | undefined>(_.initialValues?.FromPrice);
    const [toPrice, setToPrice] = useState<number | undefined>(_.initialValues?.ToPrice);

    // HANDLE: 'fromPrice' and 'toPrice' change
    useEffect(() => {
        _.onChange({ FromPrice: fromPrice, ToPrice: toPrice });
    }, [fromPrice, toPrice]);


    // - HANDLES - //

    // Dropdown open/close
    function dropdownBlur() {
        // IF clicking in main, let main onClick handle closing dropdown
        if (!mouseInsideMain && !mouseInsideDropdown) {
            setDropdownOpen(false);
        }
    }

    // BUILD: Main Text
    function buildMainText() {
        if (fromPrice != null && toPrice != null) {
            return `$${fromPrice} - $${toPrice}`
        } else if (fromPrice != null) {
            return `From $${fromPrice}`;
        } else if (toPrice != null) {
            return `To $${toPrice}`;
        } else {
            return "Price";
        }
    }


    // - MAIN - //

    return (

        <div className={`${styles.mainContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            {/* Selected Option */}
            <div
                className={`${styles.mainButton} ck-dropdown`}
                onClick={() => {
                    if (!dropdownOpen) {
                        setDropdownOpen(true);
                    } else {
                        setDropdownOpen(false);
                    }
                }}
                onMouseEnter={() => setMouseInsideMain(true)}
                onMouseLeave={() => setMouseInsideMain(false)}
            >

                {/* Dolla Sign */}
                {fromPrice == null && toPrice == null &&
                <Image
                    src={`/icons/DollarSign.svg`}
                    width={100}
                    height={100}
                    className={styles.optionIcon}
                    alt="DollarSign"
                />}

                {/* Main Text */}
                {buildMainText()}

            </div>

            {/* Dropdown */}
            {dropdownOpen &&
            <div
                className={styles.dropdown}
                onMouseEnter={() => setMouseInsideDropdown(true)}
                onMouseLeave={() => setMouseInsideDropdown(false)}
                onClick={() => mouseInsideToInput ? null : document.getElementById(`from-price-input`)?.focus() /* so can still close if click outside dropdown */}
            >
                <div>From:</div>
                <JC_Field
                    inputId={`from-price-input`} // Auto-select this input when open dropdown
                    type={FieldTypeEnum.Number}
                    iconName="DollarSign"
                    value={fromPrice}
                    defaultValue={""}
                    onChange={(newValue?:string) => setFromPrice(!JC_Utils.stringNullOrEmpty(newValue) ? +newValue! : undefined)}
                    onBlur={dropdownBlur}
                />
                <div>To:</div>
                <div onMouseEnter={() => setMouseInsideToInput(true)} onMouseLeave={() => setMouseInsideToInput(false)}>
                    <JC_Field
                        inputId={`to-price-input`}
                        type={FieldTypeEnum.Number}
                        iconName="DollarSign"
                        value={toPrice}
                        defaultValue={""}
                        onChange={(newValue?:string) => setToPrice(!JC_Utils.stringNullOrEmpty(newValue) ? +newValue! : undefined)}
                        onBlur={dropdownBlur}
                    />
                </div>
            </div>}
            {/* {dropdownOpen && options.length > 8 && <div className={styles.bottomFade} id="bottomFade"/>} */}

        </div>

    );
}