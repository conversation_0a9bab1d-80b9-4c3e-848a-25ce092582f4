import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { YT_GetArtist } from "./business";
import { auth } from "@/app/auth";
import { GetUserYtMusicTokens } from "../user/business";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get request data from query parameters
        const params = new URL(request.url).searchParams;
        const artistUrl = params.get('artistUrl');

        // Validate required parameters
        if (!artistUrl) {
            return NextResponse.json({ error: "Missing artistUrl parameter" }, { status: 400 });
        }

        // Get user's YT Music tokens
        const { authToken, cookie } = await GetUserYtMusicTokens(session.user.id);

        // Call business logic with auth credentials
        const result = await YT_GetArtist(artistUrl, authToken, cookie);

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}