import 'react-toastify/dist/ReactToastify.css';
import { v4 as uuidv4 } from 'uuid';
import { toast } from 'react-toastify';
import { isEmail, isMobilePhone } from 'validator';
import { QueryResultRow, sql } from '@vercel/postgres';
import { JC_ListPagingModel, JC_ListPagingResultModel } from './models/ComponentModels/JC_ListPagingModel';
import { _ExtendedField } from './models/_ExtendedField';
import { _ModelConstructor } from './models/_ModelRequirements';
import { PutObjectCommand, DeleteObjectCommand, GetObjectCommand, S3Client } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { JC_PutRaw } from './apiServices/JC_PutRaw';
import { D_User, UserModel } from './models/User';
import { LocalStorageKeyEnum } from './enums/LocalStorageKey';

// S3 Client setup
const s3Client = new S3Client({
    region: process.env.AWS_REGION || 'ap-southeast-2',
    credentials: {
        accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
        secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
    },
});


// ----------- //
// - General - //
// ----------- //

export class JC_Utils {

    // Check if URL matches current URL
    static isOnPage(checkUrl?:string) {
        // Check if window is defined (client-side only)
        if (typeof window === 'undefined') {
            return false;
        }
        // Extract just the path portion from the URL
        const pathname = window.location.pathname;
        // Remove leading slash if present in the pathname
        const cleanPathname = pathname.startsWith('/') ? pathname.substring(1) : pathname;
        // Compare with the checkUrl (with or without leading slash)
        return cleanPathname === checkUrl || pathname === `/${checkUrl}`;
    }

    // Check if user is on a mobile device
    static isOnMobile() {
        if (typeof navigator === 'undefined') {
            return false;
        }
        const isMobileDevice = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        return isMobileDevice;
    }

    // Get responsive path for GIF WebP images (smaller version on small screens)
    static getResponsiveGifPath(imagePath: string): string {
        // Check if the path ends with "Gif.webp"
        if (imagePath.endsWith('Gif.webp')) {
            // If on mobile, use the small version
            if (JC_Utils.isOnMobile()) {
                // Replace "Gif.webp" with "Gif [Small].webp"
                return imagePath.replace('Gif.webp', 'Gif [Small].webp');
            }
        }
        return imagePath;
    }

    // Stringify object then parse it so setState call force trigger rerender
    static parseStringify(theObject:any) {
        return JSON.parse(JSON.stringify(theObject));
    }

    // Random GUID
    static generateGuid() {
        return uuidv4();
    }

    // Check if 2 arrays are equals (does not account for order)
    static arraysEqual(array1:any[], array2:any[]) {
        return array1.length == array2.length && array1.every(x1 => array2.find(x2 => JSON.stringify(x1) == JSON.stringify(x2)));
    }

    // Check if 2 arrays of guid's are equals (does not account for order)
    static guidArraysEqual(array1:string[], array2:string[]) {
        return array1.length == array2.length && array1.every(x1 => array2.find(x2 => JSON.stringify(x1.toLowerCase()) == JSON.stringify(x2.toLowerCase())));
    }

    // Check if string is in a list of strings, ignoring casing
    static stringInListOfStrings(theString:string, theList:string[]) {
        return theList.map(s => s.toLowerCase()).includes(theString.toLowerCase());
    }

    // Convert a list of arrays into a single array
    static flattenArrays(arrays:any[][]) {
        return arrays.flat();
    }

    // Check if string not null and not empty
    static stringNullOrEmpty(inString?:string|null) {
        return inString == undefined || inString == null || inString.trim().length == 0;
    }

    // Clear all localStorage items based on LocalStorageKeyEnum
    static clearAllLocalStorage() {
        // Check if window is defined (client-side only)
        if (typeof window === 'undefined') {
            return;
        }

        // Iterate through all LocalStorageKeyEnum values
        Object.values(LocalStorageKeyEnum).forEach(key => {
            // Remove the main localStorage item
            localStorage.removeItem(key);
            // Also remove any associated reset time items (used by JC_GetList caching)
            localStorage.removeItem(`${key}_ResetTime`);
        });
    }

    /**
     * Clears all localStorage entries that start with the given table name prefix
     * This is used to invalidate cache after Create, Update, or Delete operations
     * @param tableName The table name to clear cache for (e.g., "Customer", "CustomerDefect")
     * @param modelInstance Optional model instance to also clear cache for referenced models in ExtendedFields
     */
    static clearLocalStorageForTable(tableName: string, modelInstance?: any): void {
        // Check if window is defined (client-side only)
        if (typeof window === 'undefined') {
            return;
        }

        const tablesToClear = new Set<string>([tableName]);

        // If model instance is available, also clear cache for all referenced models
        if (modelInstance && modelInstance.ExtendedFields && Array.isArray(modelInstance.ExtendedFields)) {
            modelInstance.ExtendedFields.forEach((field: any) => {
                if (field.ReferenceModel && field.ReferenceModel.tableName) {
                    tablesToClear.add(field.ReferenceModel.tableName);
                }
            });
        }

        let totalKeysRemoved = 0;

        // Clear localStorage for each table
        tablesToClear.forEach(table => {
            const prefix = `${table}_`;
            const keysToRemove: string[] = [];

            // Find all localStorage keys that start with the table prefix
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(prefix)) {
                    keysToRemove.push(key);
                }
            }

            // Remove all matching keys
            keysToRemove.forEach(key => {
                localStorage.removeItem(key);
            });

            if (keysToRemove.length > 0) {
                console.log(`Cleared ${keysToRemove.length} cache entries for table: ${table}`);
                totalKeysRemoved += keysToRemove.length;
            }
        });

        if (totalKeysRemoved > 0 && tablesToClear.size > 1) {
            console.log(`Total cache entries cleared: ${totalKeysRemoved} across ${tablesToClear.size} tables`);
        }
    }

    // Round to 2dp and cut off 0's
    static roundAndCutZeroes(num:number, dp:number) {
        if (num == null || num == 0) {
            return 0;
        }
        const newNum = parseFloat(num?.toFixed(dp));
        return Math.round(newNum * 100) / 100;
    }

    // See if search string split by words matches other string
    static searchMatches(searchString:string, checkString:string) {
        let searchWords:string[] = searchString?.toLowerCase().trim().split(' ');
        return searchWords.every(word => checkString.toLowerCase().indexOf(word) > -1);
    }

    // Toast
    static showToastError(text:string) {
        toast.error(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }
    static showToastWarning(text:string) {
        toast.warning(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }
    static showToastSuccess(text:string) {
        toast.success(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }
    static showToastInfo(text:string) {
        toast.info(text, {
            position: "top-center",
            style: { whiteSpace: 'pre-line' }
        });
    }

    // Sleep
    static async sleep(seconds:number) {
        return new Promise(r => setTimeout(r, seconds*1000));
    }

    // Check if an element is near the center of the screen
    static isElementNearScreenCenter(element: HTMLElement, thresholdPercent: number = 0.2): { isNearCenter: boolean, position: 'above' | 'center' | 'below' } {
        if (!element) return { isNearCenter: false, position: 'below' };

        const rect = element.getBoundingClientRect();
        const windowHeight = window.innerHeight;
        const elementCenter = rect.top + rect.height / 2;
        const windowCenter = windowHeight / 2;

        // Calculate how close the element is to the center (as a percentage of window height)
        const distanceFromCenter = Math.abs(elementCenter - windowCenter) / windowHeight;

        // Determine if element is above, at, or below center
        let position: 'above' | 'center' | 'below';
        if (elementCenter < windowCenter - (windowHeight * thresholdPercent)) {
            position = 'above';
        } else if (elementCenter > windowCenter + (windowHeight * thresholdPercent)) {
            position = 'below';
        } else {
            position = 'center';
        }

        // Check if element is within the threshold percentage of the center
        const isNearCenter = distanceFromCenter < thresholdPercent;

        return { isNearCenter, position };
    }

    // Cache for sound files
    private static soundFilesCache: string[] | null = null;
    private static lastCacheTime: number = 0;
    private static cacheDuration: number = 60000; // 1 minute cache duration

    // Play a random sound from the public/sounds directory
    static async playRandomSound() {
        try {
            // Get the list of sound files (from cache or API)
            const soundFiles = await this.getSoundFiles();

            if (!soundFiles || soundFiles.length === 0) {
                console.error("No sound files found");
                return;
            }

            // Select a random sound file
            const randomIndex = Math.floor(Math.random() * soundFiles.length);
            const soundFile = soundFiles[randomIndex];

            // Create and play the audio
            const audio = new Audio(`/sounds/${soundFile}`);
            audio.play();
        } catch (error) {
            console.error("Error playing sound:", error);
        }
    }

    // Get the list of sound files from the API or cache
    private static async getSoundFiles(): Promise<string[]> {
        const currentTime = Date.now();

        // Return cached files if they exist and cache hasn't expired
        if (this.soundFilesCache && (currentTime - this.lastCacheTime < this.cacheDuration)) {
            return this.soundFilesCache;
        }

        try {
            // Fetch the list of sound files from the API
            const response = await fetch('/api/sounds');

            if (!response.ok) {
                throw new Error(`Failed to fetch sound files: ${response.status}`);
            }

            const data = await response.json();

            // Update the cache
            this.soundFilesCache = data.wavFiles;
            this.lastCacheTime = currentTime;

            return data.wavFiles;
        } catch (error) {
            console.error("Error fetching sound files:", error);

            // Return empty array if there's an error
            return [];
        }
    }

    // Eg. "productVariation" -> "Product Variations"
    static routeNameToDescription(routeName:string) {
        if (routeName.indexOf('/') >= 0) {
            return routeName;
        } else {
            const result = routeName.replace(/([A-Z])/g, ' $1');
            return result.charAt(0).toUpperCase() + result.slice(1) + 's';
        }
    }

    static getNumOrdinal(num:number) {
        if (num.toString().split('.')[0].slice(-2)[0] == '1') {
            return "th";
        }
        switch (num % 10) {
            case 1:  return "st";
            case 2:  return "nd";
            case 3:  return "rd";
            default: return "th";
        }
    }

    // Find the value in a json object for a given key
    static findKeyValue(obj:any, key:string, whereCallback?:(obj:object)=>boolean):any {
        for (let prop in obj) {
            if (prop === key && (whereCallback == null || whereCallback(obj[prop]))) {
                return obj[prop];
            } else if (typeof obj[prop] === "object") {
                let value = this.findKeyValue(obj[prop], key, whereCallback);
                if (value !== undefined) {
                    return value;
                }
            }
        }
    }

    static getLocalUserId() {
        let localUserId = localStorage.getItem(LocalStorageKeyEnum.JC_LocalUserId);
        if (localUserId == null) {
            localUserId = this.generateGuid();
            let newUser:UserModel = {
                ...D_User(),
                Id: localUserId,
                Email: `localUser${localUserId}@gmail.com`
            };
            JC_PutRaw("user", { userData: newUser, password: "Letsgo.123" });
            localStorage.setItem(LocalStorageKeyEnum.JC_LocalUserId, localUserId);
        }
        return localUserId;
    }
}


// --------- //
// - DATES - //
// --------- //

export class JC_Utils_Dates {

    // Get number of minutes between 2 dates
    static minutesBetweenDates(inDate1:Date, inDate2:Date) {
        let date1 = new Date(inDate1);
        let date2 = new Date(inDate2);
        let msBetween = Math.abs(date1.getTime() - date2.getTime());
        const minutesBetween = msBetween / (60 * 1000);
        return minutesBetween;
    }

    // Get formatted date string
    static formattedDateString(inDate:Date) {
        let theDate = new Date(inDate);
        let dateNum = theDate.getDate();
        let ordinal = JC_Utils.getNumOrdinal(dateNum);
        let monthLong = theDate.toLocaleString('default', { month: 'long' });
        let year = theDate.getFullYear();
        return `${dateNum}${ordinal} ${monthLong} ${year}`;
    }

    // Format Date object for Postgres timestamp
    static formatDateForPostgres(inDate:Date) {
        const year    = inDate.getFullYear();
        const month   = (inDate.getMonth() + 1).toString().padStart(2, '0');
        const day     = inDate.getDate().toString().padStart(2, '0');
        const hours   = inDate.getHours().toString().padStart(2, '0');
        const minutes = inDate.getMinutes().toString().padStart(2, '0');
        const seconds = inDate.getSeconds().toString().padStart(2, '0');
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }

    // Format date as DD/MM/YY
    static formatDateShort(inDate: Date) {
        const theDate = new Date(inDate);
        const day = theDate.getDate().toString().padStart(2, '0');
        const month = (theDate.getMonth() + 1).toString().padStart(2, '0');
        const year = theDate.getFullYear().toString().slice(-2);
        return `${day}/${month}/${year}`;
    }

    // Format date as DD/MM/YYYY
    static formatDateFull(inDate: Date) {
        const theDate = new Date(inDate);
        const day = theDate.getDate().toString().padStart(2, '0');
        const month = (theDate.getMonth() + 1).toString().padStart(2, '0');
        const year = theDate.getFullYear();
        return `${day}/${month}/${year}`;
    }

    // Convert date to timezone-safe date for form handling
    // Sets time to noon to avoid timezone boundary issues
    static toTimezoneSafeDate(value: Date | string | undefined): Date | undefined {
        if (!value) return undefined;

        if (value instanceof Date) {
            return new Date(value.getFullYear(), value.getMonth(), value.getDate(), 12, 0, 0);
        } else if (typeof value === "string") {
            const parts = value.split("-");
            if (parts.length === 3) {
                const year = parseInt(parts[0], 10);
                const month = parseInt(parts[1], 10) - 1; // JS months are 0-based
                const day = parseInt(parts[2], 10);
                return new Date(year, month, day, 12, 0, 0); // Noon local time
            }
            return new Date(value); // fallback
        }

        return undefined;
    }

    // Format date as "Tues, 18 Feb 2025" for inspection reports
    static formatInspectionDate(date: Date): string {
        const dayNames = ['Sun', 'Mon', 'Tues', 'Wed', 'Thu', 'Fri', 'Sat'];
        const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

        const dayName = dayNames[date.getDay()];
        const day = date.getDate();
        const monthName = monthNames[date.getMonth()];
        const year = date.getFullYear();

        return `${dayName}, ${day} ${monthName} ${year}`;
    }
}


// -------------- //
// - VALIDATION - //
// -------------- //

export class JC_Utils_Validation {

    static validEmail(inEmail:string) {
        return isEmail(inEmail);
    }

    static validPhone(inPhone:string) {
        return isMobilePhone(inPhone);
    }

    // Main password validation - checks all requirements
    static validPassword(inPassword:string) {
        return this.validPasswordLength(inPassword)
        && this.validPasswordSymbol(inPassword)
        && this.validPasswordNumber(inPassword);
    }

    // Individual password validation checks
    static validPasswordLength(inPassword:string) {
        return inPassword.length >= 6; // At least 6 characters
    }

    static validPasswordSymbol(inPassword:string) {
        return /[`!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?~]/.test(inPassword); // At least 1 symbol
    }

    static validPasswordNumber(inPassword:string) {
        return /[0-9]+/.test(inPassword); // At least one number
    }

}


// ------------ //
// - BUSINESS - //
// ------------ //

export class JC_Utils_Business {

    // Generic Get function for single record retrieval
    static async sqlGet<T extends QueryResultRow>(
        modelConstructor: _ModelConstructor<T>,
        pkValue: string,
        filterDeleted: boolean = true
    ): Promise<T> {
        // Get extended fields from a temporary instance of the model
        const tempInstance = new modelConstructor();
        const extendedFields = tempInstance.ExtendedFields;

        // Build JOIN clauses and SELECT fields for extended fields
        const { joinClauses, selectFields } = this.buildExtendedFieldsQuery(extendedFields);

        const query = `
            SELECT ${selectFields}
            FROM public."${modelConstructor.tableName}" main
            ${joinClauses}
            WHERE main."${modelConstructor.primaryKey}" = $1
            ${filterDeleted ? `AND main."Deleted" = 'False'` : ''}
        `;

        let result = new modelConstructor((await sql.query(query, [pkValue])).rows[0]) as T;

        // Process setWithCallback fields
        if (extendedFields && extendedFields.length > 0) {
            const resultArray = await this.processSetWithCallbackFields([result], extendedFields);
            result = resultArray[0];
        }

        return result;
    }

    // Generic ItemExists function for checking if record exists
    static async sqlItemExists<T extends QueryResultRow>(
        modelConstructor: _ModelConstructor<T>,
        pkValue: string,
        filterDeleted: boolean = true
    ): Promise<boolean> {
        const query = `
            SELECT COUNT(*) as count
            FROM public."${modelConstructor.tableName}"
            WHERE "${modelConstructor.primaryKey}" = $1
            ${filterDeleted ? `AND "Deleted" = 'False'` : ''}
        `;
        const result = await sql.query(query, [pkValue]);
        return parseInt(result.rows[0].count) > 0;
    }

    // Generic Delete function for single record soft deletion
    static async sqlDelete<T extends QueryResultRow>(
        modelConstructor: _ModelConstructor<T>,
        pkValue: string
    ): Promise<void> {
        const query = `
            UPDATE public."${modelConstructor.tableName}"
            SET "Deleted" = $1,
                "ModifiedAt" = $2
            WHERE "${modelConstructor.primaryKey}" = $3
        `;

        await sql.query(query, ['True', new Date().toUTCString(), pkValue]);
    }

    // Build JOIN clauses and SELECT fields for extended fields
    private static buildExtendedFieldsQuery(extendedFields?: _ExtendedField[]): {
        joinClauses: string;
        selectFields: string;
    } {
        let joinClauses = '';
        let selectFields = `main.*`;

        if (extendedFields && extendedFields.length > 0) {
            const joins: string[] = [];
            const additionalSelects: string[] = [];

            extendedFields.forEach((field: _ExtendedField, index: number) => {
                // Skip extended fields that have setWithCallback - they will be processed after SQL query
                if (field.setWithCallback) {
                    return;
                }

                const alias = `ref${index}`;
                const referenceField = field.ReferenceField || field.ReferenceModel.primaryDisplayField;

                // Use the FromField property to determine the foreign key field name
                const foreignKeyField = field.FromField;

                joins.push(`
                    LEFT JOIN public."${field.ReferenceModel.tableName}" ${alias}
                    ON main."${foreignKeyField}" = ${alias}."${field.ReferenceModel.primaryKey}"`);

                additionalSelects.push(`${alias}."${referenceField}" AS "${field.Name}"`);
            });

            joinClauses = joins.join('');
            if (additionalSelects.length > 0) {
                selectFields += ', ' + additionalSelects.join(', ');
            }
        }

        return { joinClauses, selectFields };
    }

    // Process setWithCallback fields for extended fields
    private static async processSetWithCallbackFields<T extends QueryResultRow>(
        resultList: T[],
        extendedFields?: _ExtendedField[]
    ): Promise<T[]> {
        if (!extendedFields || extendedFields.length === 0) {
            return resultList;
        }

        // Find extended fields that have setWithCallback
        const callbackFields = extendedFields.filter(field => field.setWithCallback);

        if (callbackFields.length === 0) {
            return resultList;
        }

        // Process each result item
        for (const item of resultList) {
            for (const field of callbackFields) {
                if (field.setWithCallback) {
                    // Call the setWithCallback function and set the extended field value
                    const value = await field.setWithCallback(item);
                    (item as any)[field.Name] = value;
                }
            }
        }

        return resultList;
    }

}


// --------- //
// - FILES - //
// --------- //

export class JC_Utils_Files {
    static defaultSignedUrlExpiry = 60 * 5; // 5 minutes

    // Upload file buffer to S3
    static async uploadFile({
        buffer,
        key,
        contentType,
    }: {
        buffer: Buffer;
        key: string;
        contentType: string;
    }): Promise<void> {
        const command = new PutObjectCommand({
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
            Body: buffer,
            ContentType: contentType
        });

        await s3Client.send(command);
    }

    // Get a signed download URL for a file
    static async getSignedUrlForKey(
        key: string,
        expiresInSeconds: number = this.defaultSignedUrlExpiry
    ): Promise<string> {
        const command = new GetObjectCommand({
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
        });
        return await getSignedUrl(s3Client, command, { expiresIn: expiresInSeconds });
    }

    // Get a signed upload URL for a file
    static async getSignedUrlForUpload(
        key: string,
        contentType: string,
        expiresInSeconds: number = this.defaultSignedUrlExpiry
    ): Promise<string> {
        const command = new PutObjectCommand({
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
            ContentType: contentType
        });

        return await getSignedUrl(s3Client, command, { expiresIn: expiresInSeconds });
    }

    // Delete file from S3
    static async deleteFile(key: string): Promise<void> {
        const command = new DeleteObjectCommand({
            Bucket: process.env.AWS_S3_BUCKET_NAME,
            Key: key,
        });

        await s3Client.send(command);
    }

    // Get file extension from MIME type (fallback based)
    static getExtensionFromMime(mimeType: string): string {
        const map: Record<string, string> = {
            'application/pdf': 'pdf',
            'text/csv': 'csv',
            'image/jpeg': 'jpg',
            'image/png': 'png',
            'image/webp': 'webp',
            'application/zip': 'zip',
            'application/json': 'json',
        };

        return map[mimeType] ?? 'bin';
    }

    // Generate Base64 from URL
    static async generateBase64FromUrl(url: string): Promise<string> {
        const imageRes = await fetch(url);
        if (!imageRes.ok) {
            throw new Error(`Failed to fetch image for Base64 conversion (${imageRes.status})`);
        }
        const buffer = Buffer.from(await imageRes.arrayBuffer());
        return buffer.toString('base64');
    }

    // Convert base64 string to buffer
    static base64ToBuffer(base64: string): Buffer {
        const clean = base64.includes(',') ? base64.split(',')[1] : base64;
        return Buffer.from(clean, 'base64');
    }

    // Upload file directly to S3 using signed URL (front-end function)
    static async uploadFileWithSignedUrl(url: string, base64: string, contentType: string): Promise<void> {
        const buffer = this.base64ToBuffer(base64);

        const response = await fetch(url, {
            method: 'PUT',
            body: buffer,
            headers: {
                'Content-Type': contentType,
            },
        });

        if (!response.ok) {
            throw new Error(`Failed to upload file: ${response.statusText}`);
        }
    }

}


// --------------- //
// - PERMISSIONS - //
// --------------- //

export class JC_Utils_Security {
    static async checkClipboardPermission(): Promise<boolean> {
        try {
            // Check if navigator is defined (client-side only)
            if (typeof navigator === 'undefined' || typeof navigator.permissions === 'undefined') {
                return false;
            }
            if (navigator.permissions) {
                const permissionStatus = await navigator.permissions.query({ name: 'clipboard-read' as PermissionName });

                if (permissionStatus.state === 'granted') {
                    return true;
                } else if (permissionStatus.state === 'prompt') {
                    try {
                        await navigator.clipboard.readText();
                        JC_Utils.showToastInfo('Permission granted.\nPlease run function again.');
                        return false;
                    } catch (err) {
                        return false;
                    }
                } else {
                    JC_Utils.showToastError('Clipboard access is blocked. Please enable clipboard permission in your browser settings.');
                    return false;
                }
            } else {
                return false;
            }
        } catch (error) {
            console.error('Error checking clipboard permission:', error);
            return false;
        }
    }

    static async copyToClipboard(text: string): Promise<boolean> {
        try {
            // Check if navigator is defined (client-side only)
            if (typeof navigator === 'undefined') {
                return false;
            }

            if (navigator.clipboard && navigator.clipboard.writeText) {
                await navigator.clipboard.writeText(text);
                return true;
            } else {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                return true;
            }
        } catch (error) {
            console.error('Error copying to clipboard:', error);
            return false;
        }
    }

    static async readFromClipboard(): Promise<string | null> {
        try {
            // Check if navigator is defined (client-side only)
            if (typeof navigator === 'undefined') {
                return null;
            }

            if (navigator.clipboard && navigator.clipboard.readText) {
                const text = await navigator.clipboard.readText();
                return text;
            } else {
                return null;
            }
        } catch (error) {
            console.error('Error reading from clipboard:', error);
            return null;
        }
    }



    // YT Music token encryption and decryption (server-side only)
    static encryptYtMusicToken(value: string): string {
        // Import crypto dynamically to avoid client-side issues
        const crypto = require('crypto');

        const ALGORITHM = 'aes-256-gcm';
        const IV_LENGTH = 12;
        const ENCRYPTION_KEY = Buffer.from(process.env.YT_MUSIC_TOKEN_ENCRYPTION_KEY!, 'hex'); // Must be 32 bytes (256 bits)

        const iv = crypto.randomBytes(IV_LENGTH);
        const cipher = crypto.createCipheriv(ALGORITHM, ENCRYPTION_KEY, iv);

        const encrypted = Buffer.concat([
            cipher.update(value, 'utf8'),
            cipher.final(),
        ]);

        const authTag = cipher.getAuthTag();

        // Final format: [IV][authTag][encryptedData] -> base64
        return Buffer.concat([iv, authTag, encrypted]).toString('base64');
    }

    static decryptYtMusicToken(encrypted: string): string {
        // Import crypto dynamically to avoid client-side issues
        const crypto = require('crypto');

        const ALGORITHM = 'aes-256-gcm';
        const IV_LENGTH = 12;
        const ENCRYPTION_KEY = Buffer.from(process.env.YT_MUSIC_TOKEN_ENCRYPTION_KEY!, 'hex'); // Must be 32 bytes (256 bits)

        const buffer = Buffer.from(encrypted, 'base64');

        const iv = buffer.subarray(0, IV_LENGTH);
        const authTag = buffer.subarray(IV_LENGTH, IV_LENGTH + 16);
        const data = buffer.subarray(IV_LENGTH + 16);

        const decipher = crypto.createDecipheriv(ALGORITHM, ENCRYPTION_KEY, iv);
        decipher.setAuthTag(authTag);

        const decrypted = Buffer.concat([
            decipher.update(data),
            decipher.final(),
        ]);

        return decrypted.toString('utf8');
    }
}


// ------- //
// - CSS - //
// ------- //

export class JC_Utils_CSS {

    static forceHideHeaderFooter(styles:any) {
        document.getElementById("JC_header")?.classList.add(styles.forceHidden);
        document.getElementById("JC_footer")?.classList.add(styles.forceHidden);
    }

    static forceWhiteBackground(styles:any) {
        document.getElementById("rootMainContainer")?.classList.add(styles.forceWhiteBackground);
    }

    static forceRootOverflowYHidden(styles:any) {
        document.getElementById("rootMainContainer")?.classList.add(styles.forceOverflowYHidden);
    }
}


// ------------ //
// - YT Music - //
// ------------ //

export class JC_Utils_YtMusic {

    static ytMusicContext() {
        return {
            "client": {
                "clientName": "WEB_REMIX",
                "clientVersion": "1.20241016.01.00"
            }
        };
    }

    static ytMusicHeaders(authorization: string, cookie: string) {
        return {
            "accept": "*/*",
            "accept-language": "en-GB,en;q=0.9,en-US;q=0.8",
            "content-type": "application/json",
            "priority": "u=1, i",
            "sec-ch-ua": "\"Microsoft Edge\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "empty",
            "sec-fetch-mode": "same-origin",
            "sec-fetch-site": "same-origin",
            "x-goog-authuser": "0",
            "x-goog-visitor-id": "Cgt1aU02eUs1Q3JROCj8idq4BjIKCgJBVRIEGgAgNQ%3D%3D",
            "x-origin": "https://music.youtube.com",
            "x-youtube-bootstrap-logged-in": "true",
            "x-youtube-client-name": "67",
            "x-youtube-client-version": "1.20241016.01.00",
            "Referer": "https://music.youtube.com/playlist?list=PLMc_DKXs_N0P03hJrtfh0xMsN-cBMNLcN",
            "Referrer-Policy": "strict-origin-when-cross-origin",
            "authorization": authorization,
            "cookie": cookie
        };
    }

    static getPlaylistIdFromUrl(playlistUrl:string) {
        return  (new URLSearchParams(new URL(playlistUrl).search)).get("list") != null
                    ? "VL" + (new URLSearchParams(new URL(playlistUrl).search)).get("list")
                    : playlistUrl.split('/').pop()!;
    }
}