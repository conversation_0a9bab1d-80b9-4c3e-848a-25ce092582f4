@import '../../global';

.spanEntireRow {
    grid-column: 1 / -1;
}

.mainContainer {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    justify-items: center;
    row-gap: 26px;
    column-gap: 20px;

    // Error Message
    .errorSpan {
        color: $errorColor;
        font-weight: bold;
        margin: -2px 0 -16px 0;
    }

    .submitButtonOverride {
        margin-top: 10px;
    }
}