import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";
import { ArtistItemModel } from "./ArtistItem";
import { UserProcessProgressCodeEnum } from "../enums/UserProcessProgressCode";
import { UserArtistSubscriptionModel } from "./UserArtistSubscription";
import { ArtistModel } from "./Artist";

// Main
export class UserProcessProgressModel extends _Base implements _ModelRequirements {

    static tableName: string = "UserProcessProgress";
    static apiRoute: string = "userProcessProgress";
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "ProcessCode";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    
    static async Get(id: string) {
        return await JC_Get<UserProcessProgressModel>(UserProcessProgressModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<UserProcessProgressModel>(UserProcessProgressModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: UserProcessProgressModel) {
        return await JC_Put<UserProcessProgressModel>(UserProcessProgressModel, this.apiRoute, data);
    }
    static async CreateList(dataList: UserProcessProgressModel[]) {
        return await JC_PutRaw<UserProcessProgressModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "UserProcessProgress");
    }
    static async Update(data: UserProcessProgressModel) {
        return await JC_Post<UserProcessProgressModel>(UserProcessProgressModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: UserProcessProgressModel[]) {
        return await JC_PostRaw<UserProcessProgressModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "UserProcessProgress");
    }
    static async Delete(id: string) {
        return await JC_Delete(UserProcessProgressModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "UserProcessProgress");
    }
    static async AdjustItems(userId: string, processCode: UserProcessProgressCodeEnum, completedItems: any[]) {
        return await JC_PostRaw(`${this.apiRoute}/adjustItems`, { userId, processCode, completedItems }, undefined, "UserProcessProgress");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    UserId: string;
    ProcessCode: UserProcessProgressCodeEnum;
    DataJson: string|null;
    ProgressDataJson: string|null;
    ItemsCompletedJson: string|null;
    ItemsLeftJson: string|null;

    // Extended
    Data?: any;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<UserProcessProgressModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.UserId = "";
        this.ProcessCode = UserProcessProgressCodeEnum.AddAllArtist;
        this.DataJson = null;
        this.ProgressDataJson = null;
        this.ItemsCompletedJson = null;
        this.ItemsLeftJson = null;
        this.Data = null;
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new UserProcessProgressModel());
    }

    static jcFieldTypeforField(fieldName: keyof UserProcessProgressModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "UserId":
                return FieldTypeEnum.Text;
            case "ProcessCode":
                return FieldTypeEnum.Dropdown;
            case "DataJson":
                return FieldTypeEnum.Text;
            case "ProgressDataJson":
                return FieldTypeEnum.Text;
            case "ItemsCompletedJson":
                return FieldTypeEnum.Text;
            case "ItemsLeftJson":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }
}

// Default
export function D_UserProcessProgress(userId:string):UserProcessProgressModel {
    return new UserProcessProgressModel({ UserId: userId });
}


// ProcessCode: "AddAllArtist"
export class ProgressData_AddAllArtist {
    ArtistUrl: string;
    YourPlaylistUrl: string;

    constructor(init?: Partial<ProgressData_AddAllArtist>) {
        this.ArtistUrl = "";
        this.YourPlaylistUrl = "";
        Object.assign(this, init);
    }
}

// ProcessCode: "NewSongs"
export class ProgressData_NewSongs {
    YourPlaylistUrl: string;

    constructor(init?: Partial<ProgressData_NewSongs>) {
        this.YourPlaylistUrl = "";
        Object.assign(this, init);
    }
}