import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { GetUserArtistSubscription, CreateUserArtistSubscription, UpdateUserArtistSubscription } from "./business";
import { UserArtistSubscriptionModel } from "@/app/models/UserArtistSubscription";

// Get
export async function GET(request: NextRequest) {

    try {
        unstable_noStore();
        const id = new URL(request.url).searchParams.get("id") as string;
        const result:UserArtistSubscriptionModel = await GetUserArtistSubscription(id);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Create
export async function PUT(request: NextRequest) {
    try {

        const dbUserArtistSubscription:UserArtistSubscriptionModel = await request.json();
        await CreateUserArtistSubscription(dbUserArtistSubscription);
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}

// Update
export async function POST(request: NextRequest) {
    try {

        const dbUserArtistSubscription:UserArtistSubscriptionModel = await request.json();
        // First check if exists
        if ((await GetUserArtistSubscription(dbUserArtistSubscription.Id)) == null) {
            await CreateUserArtistSubscription(dbUserArtistSubscription);
        } else {
            await UpdateUserArtistSubscription(dbUserArtistSubscription);
        }
        return NextResponse.json({ status: 200 });

    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}