
// - VARIABLES - //

// Colours
$primaryColor: #CC3189;
$secondaryColor: #ff0033;
$darkPrimaryColor: #7d124f;
$veryDarkPrimaryColor: #47303d;
$pastelPrimaryColor: #FD65BB;
$pastelSecondaryColor: #FF5778;
$lightPrimaryColor: #FFB1DD;
$lightSecondaryColor: #FF7E98;
$miscellaneousColor1: #ef9c3c;
$miscellaneousColor2: #70ad47;
$offWhite: #F2F2F2;
$offBlack: #303030;
$lightGrey: #ececec;
$greyHover: #dedede;
$errorColor: #d54444;

// Screen Sizes
$teenyTinyScreenSize: 600px;
$tinyScreenSize: 790px;
$smallScreenSize: 1020px;
$mediumScreenSize: 1360px;
$largeScreenSize: 1500px;

// Page
$bodyLeftRightPadding: 30px;
$bodyTopBottomPadding: 60px;
$pageMaxWidth: 1400px;
$emptyPageHeight: 500px;
@mixin mainPageStyles {
    max-width: $pageMaxWidth;
    height: max-content;
    margin-left: 40px;
    display: grid;
    grid-template-columns: max-content max-content;
    align-items: start;
    gap: 30px;
}
@mixin processContainerStyles {
    border: solid $largeBorderWidth $primaryColor;
    border-radius: $smallBorderRadius;
    padding: 20px 30px;
    box-sizing: border-box;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
}

// Borders
$tinyBorderWidth: 2px;
$smallBorderWidth: 3px;
$largeBorderWidth: 4px;
$smallImageBorderRadius: 3px;
$tinyBorderRadius: 5px;
$smallBorderRadius: 20px;
$largeBorderRadius: 30px;

// Font
$smallFontSize: 11px;
$defaultFontSize: 13px;
$titleFontSize: 56px;
@mixin labelSmall {
    font-size: 11px;
    font-weight: bold;
}
@mixin labelLarge {
    font-size: 13px;
    font-weight: bold;
}
@mixin textOutline($width, $color) {
    text-shadow: (-$width) (-$width) 0 $color,
                   $width  (-$width) 0 $color,
                 (-$width)   $width  0 $color,
                   $width    $width  0 $color;
}

// Images
$smallImageWidth: 80px;
$smallImageHeight: 62px;

// Dropdowns
@mixin dropdownIcon {
    width: 15%;
    height: auto;
    max-height: 60%;
}
@mixin dropdownOptionContainer {
    position: relative;
    width: 100%;
    height: 40px;
    padding: 0 38px 0 10px;
    box-sizing: border-box;
    display: flex;
    justify-content: space-between;
    align-content: flex-start;
    align-items: center;
    column-gap: 10px;
    border-radius: 6px;
    border: solid $tinyBorderWidth $secondaryColor;
    background-color: $offWhite;
    overflow: hidden;

    .optionLabel {
        flex-grow: 1;
        font-size: 13px;
        text-align: left !important;
    }

    .checkbox {
        position: absolute;
        top: 50%; transform: translateY(-50%);
        right: 10px;
    }

}
@mixin dropdownDropdown {
    position: absolute;
    bottom: -4px; transform: translateY(100%);
    width: 100%;
    max-height: 375px;
    overflow-y: auto;
    box-sizing: border-box;
    border-radius: 6px;
    background: rgba(#d3d3d3, 1);
    z-index: 80;
    animation: extendHeightAnimation 0.2s ease-in-out;

    .dropdownOption {
        @include dropdownOptionContainer;
        margin-bottom: 4px;
        border-color: $primaryColor;
        cursor: pointer;
        &.selected {
            background-color: $greyHover;
            cursor: default;
        }
        &:hover { background-color: $greyHover; }
        &:last-child { margin-bottom: 0; }
    }
}
@keyframes extendHeightAnimation {
    0%   {  max-height: 0;     overflow: hidden; }
    100% {  max-height: 375px; overflow: hidden; }
}

// Hide Scrollbar
@mixin hideScrollbar {
    &::-webkit-scrollbar {
        display: none;
    }
    -ms-overflow-style: none;  /* IE and Edge */
    scrollbar-width: none;  /* Firefox */
}

// Outside Click Div
@mixin outsideClickDiv {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: $offBlack;
    z-index: 90;
    opacity: 0.15;
    transition: opacity 0.2s ease-out;
}

// Hide Scrollbar
@mixin hideScrollBar {
    &::-webkit-scrollbar {
        display: none;
    }
    & {
        -ms-overflow-style: none;  /* IE and Edge */
        scrollbar-width: none;  /* Firefox */
    }
}




// - GLOBAL CLASSES - //

.forceWhiteBackground {
    background-color: white !important;
}
.forceFullScreen {
    overflow: hidden !important;
}
.forceHidden {
    display: none !important;
}
.forceOverflowYHidden {
    overflow-y: hidden !important;
}


 // - EXPORT - //

:export {
    primaryColor: $primaryColor;
    secondaryColor: $secondaryColor;
    offWhite: $offWhite;
    offBlack: $offBlack;
    errorColor: $errorColor;
}