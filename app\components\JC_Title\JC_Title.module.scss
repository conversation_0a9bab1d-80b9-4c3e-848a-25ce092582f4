@import '../../global';

$outlineWidth: 3px;
$outlineColor: $offBlack;

.mainContainer {
    width: 100%;
    height: max-content !important;
    padding: 8px 0;
    border-radius: $largeBorderRadius;
    background-color: $primaryColor;
    text-align: center;
    font-size: 30px;
    letter-spacing: 1px;
    color: $offWhite;
    user-select: none;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);

    &.secondary {
        color: $secondaryColor;
    }
}


// - SCREEN SIZES - //

@media (max-width: $smallScreenSize) {
    .mainContainer {
        font-size: 42px !important;
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .mainContainer {
        font-size: 36px !important;
    }
}