import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class ImageCacheModel extends _Base implements _ModelRequirements {

    static tableName: string = "ImageCache";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "OriginalUrl";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<ImageCacheModel>(ImageCacheModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<ImageCacheModel>(ImageCacheModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: ImageCacheModel) {
        return await JC_Put<ImageCacheModel>(ImageCacheModel, this.apiRoute, data);
    }
    static async CreateList(dataList: ImageCacheModel[]) {
        return await JC_PutRaw<ImageCacheModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "ImageCache");
    }
    static async Update(data: ImageCacheModel) {
        return await JC_Post<ImageCacheModel>(ImageCacheModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: ImageCacheModel[]) {
        return await JC_PostRaw<ImageCacheModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "ImageCache");
    }
    static async Delete(id: string) {
        return await JC_Delete(ImageCacheModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "ImageCache");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    OriginalUrl: string;
    BlobUrl: string;
    Base64: string;
    CreatedAt: Date;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ImageCacheModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.OriginalUrl = "";
        this.BlobUrl = "";
        this.Base64 = "";
        this.CreatedAt = new Date();
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new ImageCacheModel());
    }

    static jcFieldTypeforField(fieldName: keyof ImageCacheModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "OriginalUrl":
                return FieldTypeEnum.Text;
            case "BlobUrl":
                return FieldTypeEnum.Text;
            case "Base64":
                return FieldTypeEnum.Text;
            case "CreatedAt":
                return FieldTypeEnum.Date;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.OriginalUrl;
    }
}
