import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class PaymentModel extends _Base implements _ModelRequirements {

    static tableName: string = "Payment";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Id";
    static cacheMinutes_get: number = 30;
    static cacheMinutes_getList: number = 5;

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<PaymentModel>(PaymentModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<PaymentModel>(PaymentModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: PaymentModel) {
        return await JC_Put<PaymentModel>(PaymentModel, this.apiRoute, data);
    }
    static async CreateList(dataList: PaymentModel[]) {
        return await JC_PutRaw<PaymentModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "Payment");
    }
    static async Update(data: PaymentModel) {
        return await JC_Post<PaymentModel>(PaymentModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: PaymentModel[]) {
        return await JC_PostRaw<PaymentModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "Payment");
    }
    static async Delete(id: string) {
        return await JC_Delete(PaymentModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "Payment");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    OderId: string;
    PaymentStatusCode: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<PaymentModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.OderId = "";
        this.PaymentStatusCode = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new PaymentModel());
    }

    static jcFieldTypeforField(fieldName: keyof PaymentModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "OderId":
                return FieldTypeEnum.Text;
            case "PaymentStatusCode":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Id;
    }
}