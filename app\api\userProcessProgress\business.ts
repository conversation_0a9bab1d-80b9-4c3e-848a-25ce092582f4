import { sql } from "@vercel/postgres";
import { UserProcessProgressModel } from "@/app/models/UserProcessProgress";
import { UserProcessProgressCodeEnum } from "@/app/enums/UserProcessProgressCode";


// - GET - //

export async function GetUserProcessProgress(userId:string, processCode:UserProcessProgressCodeEnum):Promise<UserProcessProgressModel> {
    let dbUserProcessProgress:UserProcessProgressModel = (await sql<UserProcessProgressModel>`
        SELECT "Id",
               "UserId",
               "ProcessCode",
               "DataJson",
               "ProgressDataJson",
               "ItemsCompletedJson",
               "ItemsLeftJson",
               "CreatedAt",
               "ModifiedAt",
               "Deleted"
        FROM public."UserProcessProgress"
        WHERE "UserId" = ${userId} AND "ProcessCode" = ${processCode}
    `).rows[0];
    if (dbUserProcessProgress?.ProgressDataJson != null) {
        dbUserProcessProgress.Data = JSON.parse(dbUserProcessProgress.ProgressDataJson);
    }
    return dbUserProcessProgress;
}


// - CREATE - //

export async function CreateUserProcessProgress(userProcessProgressData:UserProcessProgressModel) {
    await sql`
        INSERT INTO public."UserProcessProgress"
        (
            "Id",
            "UserId",
            "ProcessCode",
            "DataJson",
            "ProgressDataJson",
            "ItemsCompletedJson",
            "ItemsLeftJson",
            "CreatedAt"
        )
        VALUES
        (
            ${userProcessProgressData.Id},
            ${userProcessProgressData.UserId},
            ${userProcessProgressData.ProcessCode},
            ${userProcessProgressData.DataJson},
            ${userProcessProgressData.ProgressDataJson},
            ${userProcessProgressData.ItemsCompletedJson},
            ${userProcessProgressData.ItemsLeftJson},
            ${new Date().toUTCString()}
        )
    `;
}


// - UPDATE - //

export async function UpdateUserProcessProgress(userProcessProgressData:UserProcessProgressModel) {
    await sql`
        UPDATE public."UserProcessProgress"
        SET "UserId"              = ${userProcessProgressData.UserId},
            "ProcessCode"         = ${userProcessProgressData.ProcessCode},
            "DataJson"            = ${userProcessProgressData.DataJson},
            "ProgressDataJson"    = ${userProcessProgressData.ProgressDataJson},
            "ItemsCompletedJson"  = ${userProcessProgressData.ItemsCompletedJson},
            "ItemsLeftJson"       = ${userProcessProgressData.ItemsLeftJson},
            "ModifiedAt"          = ${new Date().toUTCString()},
            "Deleted"             = ${userProcessProgressData.Deleted}
        WHERE "Id" = ${userProcessProgressData.Id}
    `;
}


// - ADJUST ITEMS - //

export async function AdjustItems(userId: string, processCode: UserProcessProgressCodeEnum, completedItems: any[]) {
    // Get current record from database using existing function
    let dbUserProcessProgress: UserProcessProgressModel = await GetUserProcessProgress(userId, processCode);

    if (!dbUserProcessProgress) {
        throw new Error(`UserProcessProgress record not found for userId ${userId} and processCode ${processCode}`);
    }

    // Parse existing JSON arrays
    let existingCompleted: any[] = dbUserProcessProgress.ItemsCompletedJson ? JSON.parse(dbUserProcessProgress.ItemsCompletedJson) : [];
    let existingLeft: any[] = dbUserProcessProgress.ItemsLeftJson ? JSON.parse(dbUserProcessProgress.ItemsLeftJson) : [];

    // Add completed items to the completed list
    existingCompleted.push(...completedItems);

    // Remove completed items from the left list by matching Id field
    const completedIds = completedItems.map(item => item.Id);
    existingLeft = existingLeft.filter(item => !completedIds.includes(item.Id));

    // Update the database record
    await sql`
        UPDATE public."UserProcessProgress"
        SET "ItemsCompletedJson" = ${JSON.stringify(existingCompleted)},
            "ItemsLeftJson"      = ${JSON.stringify(existingLeft)},
            "ModifiedAt"         = ${new Date().toUTCString()}
        WHERE "Id" = ${dbUserProcessProgress.Id}
    `;
}