import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { YT_GetItems } from "./business";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";
import { auth } from "@/app/auth";
import { GetUserYtMusicTokens } from "../user/business";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        // Get request data from query parameters
        const params = new URL(request.url).searchParams;
        const bodyDataStr = params.get('bodyData');
        const typeStr = params.get('type');

        // Parse bodyData from JSON string
        const bodyData = bodyDataStr ? JSON.parse(bodyDataStr) : null;

        // Validate required parameters
        if (!bodyData || !typeStr) {
            return NextResponse.json({ error: "Missing required parameters" }, { status: 400 });
        }

        // Convert string to enum
        const type = YtMusicItemTypeEnum[typeStr as keyof typeof YtMusicItemTypeEnum];

        // Get user's YT Music tokens
        const { authToken, cookie } = await GetUserYtMusicTokens(session.user.id);

        // Call business logic with auth credentials
        const result = await YT_GetItems(bodyData, type, authToken, cookie);

        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}
