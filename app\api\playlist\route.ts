import { NextRequest, NextResponse } from "next/server";
import { unstable_noStore } from "next/cache";
import { GetPlaylist } from "./business";
import { auth } from "@/app/auth";
import { GetUserYtMusicTokens } from "../user/business";

export async function GET(request: NextRequest) {
    try {
        unstable_noStore();

        // Get the current user session
        const session = await auth();
        if (!session?.user?.id) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
        }

        const params = new URL(request.url).searchParams;
        const playlistUrl = params.get("playlistUrl")!;

        // Get user's YT Music tokens
        const { authToken, cookie } = await GetUserYtMusicTokens(session.user.id);

        const result = await GetPlaylist(playlistUrl, authToken, cookie);
        return NextResponse.json({ result }, { status: 200 });
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error }, { status: 500 });
    }
}