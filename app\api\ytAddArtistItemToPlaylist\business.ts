import { YT_GetItemSongs } from "../ytGetItemSongs/business";
import { YT_AddToPlaylist } from "../ytAddToPlaylist/business";
import { SongModel } from "@/app/models/Song";

export const YT_AddArtistItemToPlaylist = async function (playlistUrl:string, itemId:string, authorization: string, cookie: string) {
    // Get songs with auth headers
    let songs:SongModel[] = await YT_GetItemSongs(itemId, false, authorization, cookie);
    // Add to playlist with auth headers
    await YT_AddToPlaylist(playlistUrl, songs, authorization, cookie);
}