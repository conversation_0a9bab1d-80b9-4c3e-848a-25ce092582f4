import { PlaylistModel } from "@/app/models/Playlist";
import { J<PERSON>_Utils, JC_Utils_YtMusic } from "@/app/Utils";
import { ReplaceCachedImageUrls } from "../imageCache/business";

export async function GetPlaylist(playlistUrl: string, authorization: string, cookie: string) {

    const playlistId: string = JC_Utils_YtMusic.getPlaylistIdFromUrl(playlistUrl);

    const body = {
        "context": JC_Utils_YtMusic.ytMusicContext(),
        "browseId": playlistId,
    };

    const res = (await (await fetch('https://music.youtube.com/youtubei/v1/browse?prettyPrint=false', {
        method: 'POST',
        headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
        body: JSON.stringify(body)
    })).json());

    const playlist = new PlaylistModel({
        Id: playlistId,
        Title: JC_Utils.findKeyValue(JC_Utils.findKeyValue(res, "musicResponsiveHeaderRenderer")?.title, "text"),
        ImageUrl: JC_Utils.findKeyValue(JC_Utils.findKeyValue(res, "musicResponsiveHeaderRenderer"), "thumbnails").pop()?.url
    });

    if (playlist.Title == null) {
        return null;
    } else {
        // Use artist image cache system for playlist image
        const cachedPlaylists = await ReplaceCachedImageUrls([playlist]);
        return cachedPlaylists[0];
    }
}