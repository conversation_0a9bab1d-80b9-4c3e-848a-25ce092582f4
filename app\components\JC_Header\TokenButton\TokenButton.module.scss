@import '../../../global';

.tokenButton {
    position: absolute;
    bottom: -70px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: $primaryColor;
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: transform 0.2s, background-color 0.2s;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);

    &:hover {
        background-color: darken($primaryColor, 10%);
        transform: translateX(-50%) scale(1.1);
    }

    img {
        width: 24px;
        height: 24px;
        object-fit: contain;
    }
}
