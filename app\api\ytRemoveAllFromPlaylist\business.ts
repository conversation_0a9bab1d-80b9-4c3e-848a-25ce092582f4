// Utils
import { SongModel } from "@/app/models/Song";
import { JC_Utils, JC_Utils_YtMusic } from "@/app/Utils";
import { YT_GetItemSongs } from "../ytGetItemSongs/business";

export const YT_RemoveAllFromPlaylist = async function (playlistUrl:string, authorization: string, cookie: string) {

    let playlistId:string = JC_Utils_YtMusic.getPlaylistIdFromUrl(playlistUrl);

    // Get all Playlist Items
    let playlistItems:SongModel[] = await YT_GetItemSongs(playlistId, true, authorization, cookie);

    if (playlistItems.length > 0) {

        // Setup call
        const body = {
            "context": JC_Utils_YtMusic.ytMusicContext(),
            "actions": playlistItems.map(item => ({
                "action": "ACTION_REMOVE_VIDEO",
                "setVideoId": item.PlaylistSetVideoId
            })),
            "playlistId": playlistId.substring(2)
        }

        // Call
        try {
            await fetch(`https://music.youtube.com/youtubei/v1/browse/edit_playlist?prettyPrint=false`, {
                method: 'POST',
                headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
                body: JSON.stringify(body)
            });
        } catch (err) {
            // Try once more
            try {
                await fetch(`https://music.youtube.com/youtubei/v1/browse/edit_playlist?prettyPrint=false`, {
                    method: 'POST',
                    headers: JC_Utils_YtMusic.ytMusicHeaders(authorization, cookie),
                    body: JSON.stringify(body)
                });
            } catch (error) {
                // <>WWW<> SHOW ERROR
                console.log("Error!");
            }
        }

        // Wait a bit before adding next item to give time for YT to finish adding last items
        await JC_Utils.sleep(0.5);

    }

}