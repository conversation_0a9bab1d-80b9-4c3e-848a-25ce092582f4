import { sql } from "@vercel/postgres";
import { JC_Utils_Dates, JC_Utils_Security } from "@/app/Utils";
import { UserModel } from "../../models/User";

// - GET - //

export async function GetUser(userId:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "IsAdmin",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "Id" = ${userId}
    `).rows[0];
}

export async function GetUserByStripeId(stripeCustomerId:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "IsAdmin",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "StripeCustomerId" = ${stripeCustomerId}
    `).rows[0];
}

export async function GetUserByEmail(userEmail:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "IsAdmin",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "Email" = ${userEmail}
    `).rows[0];
}

export async function GetUserByYtMusicId(ytMusicId:string) {
    return (await sql<UserModel>`
        SELECT "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "IsAdmin",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt",
            "ModifiedAt",
            "Deleted"
        FROM public."User"
        WHERE "YtMusicId" = ${ytMusicId}
    `).rows[0];
}




// - CREATE - //

export async function CreateUser(userData:UserModel) {
    await sql`
        INSERT INTO public."User"
        (
            "Id",
            "YtMusicId",
            "FirstName",
            "LastName",
            "Email",
            "YtMusicAuthTokenHash",
            "YtMusicCookieHash",
            "IsAdmin",
            "IsEmailSubscribed",
            "IsDiscountUser",
            "StripeCustomerId",
            "IsVerified",
            "CreatedAt"
        )
        VALUES
        (
            ${userData.Id},
            ${userData.YtMusicId},
            ${userData.FirstName},
            ${userData.LastName},
            ${userData.Email},
            ${userData.YtMusicAuthTokenHash},
            ${userData.YtMusicCookieHash},
            ${userData.IsAdmin},
            ${userData.IsEmailSubscribed},
            ${userData.IsDiscountUser},
            ${userData.StripeCustomerId},
            ${userData.IsVerified},
            ${new Date().toUTCString()}
        )
    `
}


// - UPDATE - //

export async function UpdateUser(userData:UserModel) {
    await sql`
        UPDATE public."User"
        SET "YtMusicId"         = ${userData.YtMusicId},
            "FirstName"         = ${userData.FirstName},
            "LastName"          = ${userData.LastName},
            "Email"             = ${userData.Email},
            "Phone"             = ${userData.Phone},
            "CompanyName"       = ${userData.CompanyName},
            "IsEmailSubscribed" = ${userData.IsEmailSubscribed},
            "StripeCustomerId"  = ${userData.StripeCustomerId},
            "ModifiedAt"        = ${new Date().toUTCString()},
            "Deleted"           = ${userData.Deleted}
        WHERE "Id" = ${userData.Id}
    `;
}



export async function UpdateUserStripeCustomerId(userId:string, newCustomerId:string) {
    await sql`
        UPDATE public."User"
        SET "StripeCustomerId" = ${newCustomerId}
        WHERE "Id" = ${userId}
    `;
}



export async function SetUserVerificationToken(userId:string, verificationToken:string) {
    await sql`
        UPDATE public."User"
        SET "VerificationToken" = ${verificationToken}
        WHERE "Id" = ${userId}
    `;
}

export async function SetUserIsVerified(userId:string) {
    await sql`
        UPDATE public."User"
        SET "IsVerified" = true
        WHERE "Id" = ${userId}
    `;
}

export async function UpdateUserYtMusicTokens(userId:string, authTokenHash:string, cookieHash:string) {
    await sql`
        UPDATE public."User"
        SET "YtMusicAuthTokenHash" = ${authTokenHash},
            "YtMusicCookieHash" = ${cookieHash},
            "ModifiedAt" = ${new Date().toUTCString()}
        WHERE "Id" = ${userId}
    `;
}

export async function GetUserYtMusicTokens(userId:string): Promise<{authToken: string, cookie: string}> {
    const user = await GetUser(userId);

    if (!user.YtMusicAuthTokenHash || !user.YtMusicCookieHash) {
        throw new Error("User does not have YT Music tokens configured");
    }

    // Decrypt the tokens
    const authToken = JC_Utils_Security.decryptYtMusicToken(user.YtMusicAuthTokenHash);
    const cookie = JC_Utils_Security.decryptYtMusicToken(user.YtMusicCookieHash);

    return { authToken, cookie };
}