import { YT_GetItems } from "../ytGetItems/business";
import { PlaylistModel } from "@/app/models/Playlist";
import { YtMusicItemTypeEnum } from "@/app/enums/YtMusicItemType";

export const YT_GetPlaylists = async function (authorization: string, cookie: string) : Promise<PlaylistModel[]> {

    // Get Playlists
    let playlists = (await YT_GetItems({ "browseId": "FEmusic_liked_playlists" }, YtMusicItemTypeEnum.Playlist, authorization, cookie)) as PlaylistModel[];
    playlists = playlists.filter(p => p.Id);

    // Get rid of "VL" at beginning of every Id
    playlists.forEach(p => p.Id = p.Id.slice(2))

    // Return
    return playlists;

}