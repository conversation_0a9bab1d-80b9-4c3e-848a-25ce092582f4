import { YtMusicItemTypeEnum } from "../enums/YtMusicItemType";
import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class ArtistItemModel extends _Base implements _ModelRequirements {

    static tableName: string = "ArtistItem";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Title";
    // static cacheMinutes_get: number = 30;
    // static cacheMinutes_getList: number = 5;

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<ArtistItemModel>(ArtistItemModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<ArtistItemModel>(ArtistItemModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: ArtistItemModel) {
        return await JC_Put<ArtistItemModel>(ArtistItemModel, this.apiRoute, data);
    }
    static async CreateList(dataList: ArtistItemModel[]) {
        return await JC_PutRaw<ArtistItemModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "ArtistItem");
    }
    static async Update(data: ArtistItemModel) {
        return await JC_Post<ArtistItemModel>(ArtistItemModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: ArtistItemModel[]) {
        return await JC_PostRaw<ArtistItemModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "ArtistItem");
    }
    static async Delete(id: string) {
        return await JC_Delete(ArtistItemModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "ArtistItem");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    PlaylistId: string;
    Type: YtMusicItemTypeEnum;
    Title: string;
    Year: number|null;
    ImageUrl: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<ArtistItemModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.PlaylistId = "";
        this.Type = YtMusicItemTypeEnum.Album;
        this.Title = "";
        this.Year = null;
        this.ImageUrl = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new ArtistItemModel());
    }

    static jcFieldTypeforField(fieldName: keyof ArtistItemModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "PlaylistId":
                return FieldTypeEnum.Text;
            case "Type":
                return FieldTypeEnum.Text;
            case "Title":
                return FieldTypeEnum.Text;
            case "Year":
                return FieldTypeEnum.Number;
            case "ImageUrl":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return this.Title;
    }
}