import { JC_Get } from "../apiServices/JC_Get";
import { JC_GetList } from "../apiServices/JC_GetList";
import { JC_GetRaw } from "../apiServices/JC_GetRaw";
import { JC_Post } from "../apiServices/JC_Post";
import { JC_PostRaw } from "../apiServices/JC_PostRaw";
import { JC_Put } from "../apiServices/JC_Put";
import { JC_PutRaw } from "../apiServices/JC_PutRaw";
import { JC_Delete } from "../apiServices/JC_Delete";
import { JC_Utils } from "../Utils";
import { _Base } from "./_Base";
import { _ExtendedField } from "./_ExtendedField";
import { _ModelRequirements } from "./_ModelRequirements";
import { JC_ListPagingModel } from "./ComponentModels/JC_ListPagingModel";
import { FieldTypeEnum } from "../enums/FieldType";

export class SongModel extends _Base implements _ModelRequirements {

    static tableName: string = "Song";
    static apiRoute: string = this.tableName.toLowerCase();
    static primaryKey: string = "Id";
    static primaryDisplayField: string = "Title";

    // - -------- - //
    // - SERVICES - //
    // - -------- - //
    static async Get(id: string) {
        return await JC_Get<SongModel>(SongModel, this.apiRoute, { id });
    }
    static async ItemExists(id: string) {
        const exists = await JC_GetRaw<boolean>(`${this.apiRoute}/itemExists`, { id });
        return { exists };
    }
    static async GetList(paging?:JC_ListPagingModel, abortSignal?: AbortSignal) {
        return await JC_GetList<SongModel>(SongModel, `${this.apiRoute}/getList`, paging, {}, abortSignal);
    }
    static async Create(data: SongModel) {
        return await JC_Put<SongModel>(SongModel, this.apiRoute, data);
    }
    static async CreateList(dataList: SongModel[]) {
        return await JC_PutRaw<SongModel[]>(`${this.apiRoute}/createList`, dataList, undefined, "Song");
    }
    static async Update(data: SongModel) {
        return await JC_Post<SongModel>(SongModel, this.apiRoute, data);
    }
    static async UpdateList(dataList: SongModel[]) {
        return await JC_PostRaw<SongModel[]>(`${this.apiRoute}/updateList`, dataList, undefined, "Song");
    }
    static async Delete(id: string) {
        return await JC_Delete(SongModel, this.apiRoute, id);
    }
    static async DeleteList(ids: string[]) {
        return await JC_PostRaw(`${this.apiRoute}/deleteList`, { ids }, undefined, "Song");
    }

    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    Id: string;
    PlaylistSetVideoId?: string;
    Title: string;
    Artist: string;
    ImageUrl: string;

    // Extended Fields (required by _ModelRequirements interface)
    ExtendedFields: _ExtendedField[] = [];

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<SongModel>) {
        super(init);
        this.Id = JC_Utils.generateGuid();
        this.PlaylistSetVideoId = undefined;
        this.Title = "";
        this.Artist = "";
        this.ImageUrl = "";
        Object.assign(this, init);
    }

    static getKeys() {
        return Object.keys(new SongModel());
    }

    static jcFieldTypeforField(fieldName: keyof SongModel) {
        switch (fieldName) {
            case "Id":
                return FieldTypeEnum.Text;
            case "PlaylistSetVideoId":
                return FieldTypeEnum.Text;
            case "Title":
                return FieldTypeEnum.Text;
            case "Artist":
                return FieldTypeEnum.Text;
            case "ImageUrl":
                return FieldTypeEnum.Text;
            default:
                return FieldTypeEnum.Text;
        }
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        return `${this.Title} - ${this.Artist}`;
    }
}