@import '../../global';

$leftGap: 10px;

.mainContainer {
    width: max-content;
    display: flex;
    align-items: center;
    column-gap: 12px;
    color: $primaryColor;
    user-select: none;
    &, .checkbox, label { cursor: pointer; }

    .checkbox {
        width: 11px;
        height: 11px;
        outline: solid $tinyBorderWidth $primaryColor;
        border-radius: 1px;
        position: relative;
        
        .innerCheckedSquare {
            position: absolute;
            left: 50%; top: 50%; transform: translate(-50%, -50%);
            z-index: 999;
            width: 60%;
            height: 60%;
            border-radius: 1px;
            background-color: $primaryColor;
        }
    }
}