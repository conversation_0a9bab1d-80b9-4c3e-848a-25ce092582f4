export class JC_PriceFilterModel {
    // - --------- - //
    // - VARIABLES - //
    // - --------- - //

    FromPrice?: number;
    ToPrice?: number;

    // - ----------- - //
    // - CONSTRUCTOR - //
    // - ----------- - //

    constructor(init?: Partial<JC_PriceFilterModel>) {
        this.FromPrice = undefined;
        this.ToPrice = undefined;
        Object.assign(this, init);
    }

    // - ------ - //
    // - STRING - //
    // - ------ - //

    toString() {
        if (this.FromPrice != null && this.ToPrice != null) {
            return `$${this.FromPrice} - $${this.ToPrice}`;
        } else if (this.FromPrice != null) {
            return `From $${this.FromPrice}`;
        } else if (this.ToPrice != null) {
            return `To $${this.ToPrice}`;
        } else {
            return "Price";
        }
    }
}