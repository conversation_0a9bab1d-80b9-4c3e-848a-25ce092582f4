import styles from "./layout.module.scss";
import type { Metadata } from "next";
import { SpeedInsights } from '@vercel/speed-insights/next';
import { Inter, Special_Elite, Margarine } from 'next/font/google';
import { ToastContainer } from "react-toastify";
import { SessionProvider } from "next-auth/react";
import { auth } from "./auth";


// Site Metadata
export const metadata: Metadata = {
    title: "YT Music Automator",
    description: "YT Music Automator",
};

// Font
const inter = Inter({ subsets: ["latin"], variable: '--font-inter' });
const kaushanScript = Special_Elite({ weight: "400", subsets: ["latin"], variable: '--font-kaushan-script' });
const shadowsIntoLight = Margarine({ weight: "400", subsets: ["latin"], variable: '--title-font' });

// Site Root
export default async function Layout_Root(_: Readonly<{
    children: React.ReactNode;
}>) {

    const session = await auth();

    return (
        <html lang="en">

            <body className={`${styles.rootMainContainer} ${inter.variable} ${kaushanScript.variable} ${shadowsIntoLight.variable}`} id="rootMainContainer">

                <div className={styles.mainTitle}>YT Music Automator</div>

                <div className={styles.pageContainer}>
                    <SessionProvider session={session}>
                        {_.children}
                    </SessionProvider>
                </div>

                <ToastContainer />

                <SpeedInsights />

            </body>

        </html>
  );
}
