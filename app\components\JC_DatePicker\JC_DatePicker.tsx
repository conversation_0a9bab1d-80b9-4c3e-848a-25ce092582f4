"use client"

import styles from "./JC_DatePicker.module.scss";
import 'react-date-range/dist/styles.css';
import 'react-date-range/dist/theme/default.css';
import React, { useState } from 'react';
import { Calendar, DateRange, DateRangePicker, Range, RangeKeyDict } from 'react-date-range';
import JC_Field from '../JC_Field/JC_Field';
import { JC_Utils, JC_Utils_Dates } from '@/app/Utils';
import { FieldTypeEnum } from '@/app/enums/FieldType';

export default function JC_DatePicker(_: Readonly<{

    overrideClass?: string;
    fieldOverrideClass?: string;
    inputId: string;
    theDate: Date;
    onChange: (newDate:Date) => void;

}>) {

    // - STATE - //

    const [pickerOpen, setPickerOpen] = useState<boolean>(false);


    // - MAIN - //

    return (
        <div key={_.theDate.toString()} className={`${styles.mainContainer} ${!JC_Utils.stringNullOrEmpty(_.overrideClass) ? _.overrideClass : ''}`}>

            {/* Outside Click Div */}
            {pickerOpen && <div className={styles.outsideClick} onClick={() => setPickerOpen(false)} />}

            {/* Field */}
            <JC_Field
                overrideClass={_.fieldOverrideClass}
                inputOverrideClass={styles.inputOverride}
                inputId={_.inputId}
                type={FieldTypeEnum.Text}
                label="Date Mate"
                value={JC_Utils_Dates.formattedDateString(_.theDate)}
                readOnly
                onClick={() => { setPickerOpen(!pickerOpen); }}
            />

            {/* Date Picker */}
            {pickerOpen &&
            <Calendar
                className={styles.datePicker}
                showMonthAndYearPickers={false}
                date={_.theDate}
                color={styles.primaryColor}
                onChange={(newDate:Date) => _.onChange(newDate)}
            />}

        </div>
    );
}