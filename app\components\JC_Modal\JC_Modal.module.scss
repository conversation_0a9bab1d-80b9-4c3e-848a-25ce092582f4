@import '../../global';

$topBottomPadding: 26px;

// Black background overlay
.blackOverlay {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0; left: 0;
    background-color: $offBlack;
    opacity: 0.35;
}

// Modal
.modalContainer {
    z-index: 999;
    width: max-content;
    padding: $topBottomPadding 30px $topBottomPadding 30px;
    max-width: calc(100% - 12px);
    max-height: calc(100vh - 100px);
    position: fixed;
    left: 50%; top: 50%; transform: translate(-50%, -50%);
    background-color: $offBlack;
    outline: solid $largeBorderWidth $offWhite;
    border-radius: $smallBorderRadius;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.4);
    color: $offWhite;
    box-sizing: border-box;
    overflow-y: auto;
    overflow-x: hidden;
    @include hideScrollBar;
}
.modalContainer.forceTransparent {
    background-color: transparent;
    outline: none;
}

// Title
.title {
    margin: calc(16px - $topBottomPadding) auto 16px auto;
    width: 80%;
    text-align: center;
    font-size: 28px;
    font-weight: bold;
}

// Cancel button
.cancelButton {
    position: absolute;
    right: -42px;
    top: 2px;
    width: 18px;
    height: auto;
    padding: 5px;
    outline: none;
    background-color: $offBlack;
    border: none;
    border-radius: 50%;
    color: $offWhite;
    font-size: 27px;
    font-weight: bold;
    cursor: pointer;
}