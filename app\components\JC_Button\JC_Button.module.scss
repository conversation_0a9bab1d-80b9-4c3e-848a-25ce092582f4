@import '../../global';

.spinnerContainer {
    height: 44px;
    width: 100%;
}

.buttonContainer {
    min-width: 130px;
    width: max-content;
    height: 44px;
    padding: 0 20px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    background-color: $offWhite;
    outline-color: $offBlack;
    outline-width: $smallBorderWidth;
    outline-style: solid;
    border-radius: $largeBorderRadius;
    font-size: 20px;
    font-weight: bold;
    overflow: visible;
    user-select: none;
    cursor: pointer;
    transition: outline-width  0.15s,
                outline-color  0.35s,
                outline-offset 0.15s;

    // Text
    .buttonText {
        width: max-content;
        overflow: visible;
        color: black;
    }

    // Hover
    &:hover {
        outline-width: $largeBorderWidth;
        outline-color: $secondaryColor;
        outline-offset: -1px;
        transition: outline-width  0.04s,
                    outline-color  0.08s,
                    outline-offset 0.04s;
    }

    // Selected
    &.buttonSelected {
        outline-width: $largeBorderWidth;
        outline-color: $pastelSecondaryColor;
        outline-offset: -1px;
        cursor: default !important;
        transition: outline-width 0s,
                    outline-color 0s;
    }

    // Icon
    &.includeIcon {
        column-gap: 8px;
        justify-content: space-between;
        .buttonIcon {
            width: 20px;
            height: auto;
            margin-left: -5px;
        }
        .buttonText {
            position: static;
            transform: translate(0, 0);
            flex-grow: 1;
        }
    }

    // Icon Only
    &.iconOnly {
        min-width: 0;
        padding: 0 15px;
        border-radius: $tinyBorderRadius;
        .buttonIcon {
            margin-left: 0;
        }
    }

    // Icon On Top
    &.iconOnTop {
        flex-direction: column;
        padding: 5px 0;
        height: max-content;
        min-height: 82px;
        row-gap: 5px;
        justify-content: space-between;
        .buttonText {
            flex-grow: 0;
        }
        .buttonIcon {
            margin-top: 2px;
            height: 40px;
            width: auto;
            margin-left: 0;
        }
    }

    // Secondary
    &.secondary {
        outline-color: transparent;
        background-color: $pastelPrimaryColor;
        &:hover { outline-color: $secondaryColor; }
        &.buttonSelected { outline-color: $pastelSecondaryColor !important; }
    }

    // Small
    &.small {
        min-width: 60px;
        height: 34px;
        padding: 0 14px;
        font-size: 14px;
        // font-weight: normal;
        &:hover { outline-width: $smallBorderWidth; }
        &.buttonSelected { outline-width: $smallBorderWidth }
    }

    // Disabled
    &.disabled {
        outline: none !important;
        // background-color: $greyHover;
        opacity: 0.7;
        cursor: inherit;
        &:hover {
            outline-width: $smallBorderWidth;
            outline-color: $primaryColor;
        }
    }
}


// - SCREEN SIZES - //

@media (max-width: $tinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 115px;
        height: 36px;
        font-size: 16px;

        .buttonIcon {
            width: 17px !important;
        }
    }
}

@media (max-width: $teenyTinyScreenSize) {
    .enableResponsive.buttonContainer {
        min-width: 95px;
        height: 30px;
        padding: 0 10px;
        font-size: 13px;
        outline-width: $tinyBorderWidth;

        &:hover {
            outline-width: $smallBorderWidth;
        }
        &.buttonSelected {
            outline-width: $smallBorderWidth;
        }
        .buttonIcon {
            width: 13px !important;
            margin-left: 0 !important;
            margin-right: -2px !important;
        }
    }
}